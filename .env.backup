# Alpha Grid API Configuration
# Free tier API keys for cryptocurrency data

# CoinGecko API (Market data, new listings)
COINGECKO_API_KEY=CG-wJx8SHeFD1KjG3F73LB14967

# Etherscan API (Ethereum blockchain data)
ETHERSCAN_API_KEY=**********************************

# Web3 Provider (Alchemy - Multi-chain data)
WEB3_PROVIDER_URL=https://eth-mainnet.g.alchemy.com/v2/BwYx2Nu9Fo8TBdqPmMw18bIz_MWdsQP-

# Dune Analytics (Custom blockchain analytics)
DUNE_API_KEY=8L1zlFUtMWg4FCMsZuFkKIkXbRpPYefv

# Messari API (Fundamental crypto data)
MESSARI_API_KEY=cCEzIxHe0B60We-z-k7BNrmst8AUwxFX2LnrJbl+IStK7aGK

# Coinbase Advanced Trade API (Real-time market data)
COINBASE_API_KEY_NAME=organizations/e5f76431-e547-4abe-bb89-d066b075f396/apiKeys/2078a302-1c03-4332-91d0-2b568111199d
*****************************************************************************************************************************************************************************************************************************************************************

# Application Settings
LOG_LEVEL=INFO
DATABASE_PATH=data/alpha_grid.db
MAX_WORKERS=8

COINBASE_API_KEY_NAME=organizations/e5f76431-e547-4abe-bb89-d066b075f396/apiKeys/2078a302-1c03-4332-91d0-2b568111199d
*****************************************************************************************************************************************************************************************************************************************************************

"INFURA KEY=********************************

API Key Secret=W5FLxQOyFK48NG17EGlRbavddP9WXDkCDTNPPAPNkzl/fnR14qjcNg
