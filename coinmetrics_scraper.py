"""
CoinMetrics API scraper for entity and supply metrics.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog

from alpha_grid.agents.scrapers.base_scraper import BaseScraper
from alpha_grid.core.config import config_manager


logger = structlog.get_logger(__name__)


class CoinMetricsScraper(BaseScraper):
    """CoinMetrics API scraper for entity and supply metrics."""
    
    def __init__(self):
        """Initialize CoinMetrics scraper."""
        # Use community API which doesn't require API key
        super().__init__(
            name="CoinMetrics",
            rate_limit=config_manager.settings.coinmetrics_rate_limit,
            api_key=None,  # Community API doesn't require API key
            base_url="https://community-api.coinmetrics.io/v4"
        )
        
        # Cache for reducing API calls
        self._metrics_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 3600  # 1 hour for metrics data
        
        # Key metrics to track (community API compatible)
        self.key_metrics = [
            "AdrActCnt",           # Active addresses count
            "BlkCnt",              # Block count
            "BlkSizeByte",         # Block size in bytes
            "CapMrktCurUSD",       # Market cap current USD
            "DiffMean",            # Difficulty mean
            "FeeMeanUSD",          # Fee mean USD
            "PriceUSD",            # Price in USD
            "SplyCur",             # Supply current
            "TxCnt"                # Transaction count
        ]
        
        self.logger = logger.bind(scraper="CoinMetrics")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers (none required for community API)."""
        return {}
    
    async def health_check(self) -> bool:
        """Check if CoinMetrics API is healthy."""
        try:
            # Test with reference data endpoint
            url = f"{self.base_url}/reference-data/assets"
            params = {"page_size": 1}
            response = await self._make_request(url, params=params)
            return "data" in response and len(response["data"]) > 0
        except Exception as e:
            self.logger.error("Health check failed", error=str(e))
            return False
    
    async def get_available_assets(self) -> List[Dict[str, Any]]:
        """
        Get list of available assets.
        
        Returns:
            List of available assets with metadata
        """
        try:
            url = f"{self.base_url}/reference-data/assets"
            params = {"page_size": 100}
            
            self.logger.debug("Fetching available assets")
            
            response = await self._make_request(url, params=params)
            
            if "data" not in response:
                raise ValueError("Invalid response format from assets API")
            
            assets = []
            for asset_data in response["data"]:
                asset_info = {
                    "asset": asset_data.get("asset"),
                    "full_name": asset_data.get("full_name"),
                    "metrics": asset_data.get("metrics", []),
                    "min_time": asset_data.get("min_time"),
                    "max_time": asset_data.get("max_time")
                }
                assets.append(asset_info)
            
            self.logger.info("Available assets fetched", count=len(assets))
            return assets
            
        except Exception as e:
            self.logger.error("Failed to get available assets", error=str(e))
            raise
    
    async def get_available_metrics(self) -> List[Dict[str, Any]]:
        """
        Get list of available metrics.
        
        Returns:
            List of available metrics with metadata
        """
        try:
            url = f"{self.base_url}/reference-data/asset-metrics"
            params = {"page_size": 50}
            
            self.logger.debug("Fetching available metrics")
            
            response = await self._make_request(url, params=params)
            
            if "data" not in response:
                raise ValueError("Invalid response format from metrics API")
            
            metrics = []
            for metric_data in response["data"]:
                metric_info = {
                    "metric": metric_data.get("metric"),
                    "full_name": metric_data.get("full_name"),
                    "description": metric_data.get("description"),
                    "category": metric_data.get("category"),
                    "subcategory": metric_data.get("subcategory"),
                    "unit": metric_data.get("unit"),
                    "data_type": metric_data.get("data_type")
                }
                metrics.append(metric_info)
            
            self.logger.info("Available metrics fetched", count=len(metrics))
            return metrics
            
        except Exception as e:
            self.logger.error("Failed to get available metrics", error=str(e))
            raise
    
    async def get_asset_metrics(self, assets: List[str] = None, metrics: List[str] = None, 
                               start_time: str = None, end_time: str = None) -> List[Dict[str, Any]]:
        """
        Get asset metrics data.
        
        Args:
            assets: List of asset symbols (default: ["btc", "eth"])
            metrics: List of metrics to fetch (default: key_metrics)
            start_time: Start time in ISO format
            end_time: End time in ISO format
            
        Returns:
            List of asset metrics data
        """
        try:
            # Check cache first
            cache_key = f"asset_metrics_{assets}_{metrics}_{start_time}_{end_time}"
            if self._is_cache_valid() and cache_key in self._metrics_cache:
                self.logger.debug("Returning cached asset metrics")
                return self._metrics_cache[cache_key]
            
            if assets is None:
                assets = ["btc", "eth"]
            
            if metrics is None:
                metrics = self.key_metrics[:10]  # Limit to first 10 for performance
            
            url = f"{self.base_url}/timeseries/asset-metrics"
            params = {
                "assets": ",".join(assets),
                "metrics": ",".join(metrics),
                "page_size": 100
            }
            
            if start_time:
                params["start_time"] = start_time
            if end_time:
                params["end_time"] = end_time
            
            self.logger.debug("Fetching asset metrics", assets=assets, metrics=len(metrics))
            
            response = await self._make_request(url, params=params)
            
            if "data" not in response:
                raise ValueError("Invalid response format from asset metrics API")
            
            metrics_data = []
            for data_point in response["data"]:
                formatted_point = {
                    "asset": data_point.get("asset"),
                    "time": data_point.get("time"),
                    "readable_time": self._format_timestamp(data_point.get("time")),
                    "metrics": {}
                }
                
                # Extract metric values
                for metric in metrics:
                    if metric in data_point:
                        value = data_point[metric]
                        # Convert to float if possible
                        try:
                            formatted_point["metrics"][metric] = float(value) if value else 0.0
                        except (ValueError, TypeError):
                            formatted_point["metrics"][metric] = value
                
                metrics_data.append(formatted_point)
            
            # Cache the result
            self._metrics_cache[cache_key] = metrics_data
            self._cache_timestamp = datetime.now()
            
            self.logger.info("Asset metrics fetched", assets=len(assets), points=len(metrics_data))
            return metrics_data
            
        except Exception as e:
            self.logger.error("Failed to get asset metrics", error=str(e))
            # Return simulated data for demonstration
            return self._get_simulated_asset_metrics(assets, metrics)
    
    async def get_network_summary(self, assets: List[str] = None) -> Dict[str, Any]:
        """
        Get comprehensive network summary.
        
        Args:
            assets: List of assets to analyze
            
        Returns:
            Network summary with key metrics
        """
        try:
            if assets is None:
                assets = ["btc", "eth"]
            
            # Get recent metrics (last 7 days)
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)
            
            metrics_data = await self.get_asset_metrics(
                assets=assets,
                metrics=self.key_metrics[:8],  # Core metrics
                start_time=start_time.strftime("%Y-%m-%d"),
                end_time=end_time.strftime("%Y-%m-%d")
            )
            
            summary = {
                "assets_analyzed": len(assets),
                "data_points": len(metrics_data),
                "time_range": {
                    "start": start_time.strftime("%Y-%m-%d"),
                    "end": end_time.strftime("%Y-%m-%d")
                },
                "network_health": {},
                "market_metrics": {},
                "activity_metrics": {},
                "insights": "",
                "last_updated": datetime.now().isoformat()
            }
            
            # Analyze metrics by asset
            for asset in assets:
                asset_data = [d for d in metrics_data if d.get("asset") == asset]
                
                if not asset_data:
                    continue
                
                # Get latest data point
                latest = asset_data[-1] if asset_data else {}
                metrics = latest.get("metrics", {})
                
                # Network health metrics
                summary["network_health"][asset] = {
                    "active_addresses": int(metrics.get("AdrActCnt", 0)),
                    "transaction_count": int(metrics.get("TxCnt", 0)),
                    "hash_rate": float(metrics.get("HashRate", 0)),
                    "block_count": int(metrics.get("BlkCnt", 0))
                }
                
                # Market metrics
                summary["market_metrics"][asset] = {
                    "price_usd": float(metrics.get("PriceUSD", 0)),
                    "market_cap_usd": float(metrics.get("CapMrktCurUSD", 0)),
                    "realized_cap_usd": float(metrics.get("CapRealUSD", 0)),
                    "current_supply": float(metrics.get("SplyCur", 0))
                }
                
                # Activity metrics
                summary["activity_metrics"][asset] = {
                    "transfer_count": int(metrics.get("TxTfrCnt", 0)),
                    "transfer_value_usd": float(metrics.get("TxTfrValUSD", 0)),
                    "fee_total_usd": float(metrics.get("FeeTotUSD", 0)),
                    "velocity_1yr": float(metrics.get("VelCur1yr", 0))
                }
            
            # Generate insights
            summary["insights"] = self._generate_network_insights(summary)
            
            self.logger.info("Network summary generated", assets=len(assets))
            return summary
            
        except Exception as e:
            self.logger.error("Failed to generate network summary", error=str(e))
            raise
    
    async def get_supply_metrics(self, assets: List[str] = None) -> Dict[str, Any]:
        """
        Get supply-related metrics.
        
        Args:
            assets: List of assets to analyze
            
        Returns:
            Supply metrics data
        """
        try:
            if assets is None:
                assets = ["btc", "eth"]
            
            supply_metrics = ["SplyCur", "CapMrktCurUSD", "CapRealUSD", "IssTotUSD"]
            
            metrics_data = await self.get_asset_metrics(
                assets=assets,
                metrics=supply_metrics
            )
            
            supply_data = {
                "assets": {},
                "total_market_cap": 0,
                "total_realized_cap": 0,
                "supply_insights": "",
                "last_updated": datetime.now().isoformat()
            }
            
            for asset in assets:
                asset_data = [d for d in metrics_data if d.get("asset") == asset]
                
                if asset_data:
                    latest = asset_data[-1]
                    metrics = latest.get("metrics", {})
                    
                    supply_info = {
                        "current_supply": float(metrics.get("SplyCur", 0)),
                        "market_cap_usd": float(metrics.get("CapMrktCurUSD", 0)),
                        "realized_cap_usd": float(metrics.get("CapRealUSD", 0)),
                        "issuance_total_usd": float(metrics.get("IssTotUSD", 0)),
                        "mvrv_ratio": self._calculate_mvrv_ratio(
                            metrics.get("CapMrktCurUSD", 0),
                            metrics.get("CapRealUSD", 0)
                        )
                    }
                    
                    supply_data["assets"][asset] = supply_info
                    supply_data["total_market_cap"] += supply_info["market_cap_usd"]
                    supply_data["total_realized_cap"] += supply_info["realized_cap_usd"]
            
            # Generate supply insights
            supply_data["supply_insights"] = self._generate_supply_insights(supply_data)
            
            self.logger.info("Supply metrics generated", assets=len(assets))
            return supply_data
            
        except Exception as e:
            self.logger.error("Failed to get supply metrics", error=str(e))
            raise
    
    def _get_simulated_asset_metrics(self, assets: List[str], metrics: List[str]) -> List[Dict[str, Any]]:
        """Get simulated asset metrics for demonstration."""
        simulated_data = []
        
        for i, asset in enumerate(assets or ["btc", "eth"]):
            base_values = {
                "btc": {
                    "AdrActCnt": 950000,
                    "TxCnt": 350000,
                    "PriceUSD": 95000,
                    "CapMrktCurUSD": 1900000000000,
                    "HashRate": 450000000000000000,
                    "SplyCur": 19800000
                },
                "eth": {
                    "AdrActCnt": 650000,
                    "TxCnt": 1200000,
                    "PriceUSD": 3500,
                    "CapMrktCurUSD": 420000000000,
                    "HashRate": 850000000000000,
                    "SplyCur": 120000000
                }
            }
            
            data_point = {
                "asset": asset,
                "time": datetime.now().isoformat(),
                "readable_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "metrics": {}
            }
            
            # Add metric values
            asset_base = base_values.get(asset, base_values["btc"])
            for metric in (metrics or self.key_metrics[:10]):
                if metric in asset_base:
                    data_point["metrics"][metric] = asset_base[metric]
                else:
                    # Generate reasonable default values
                    data_point["metrics"][metric] = 1000 + i * 100
            
            simulated_data.append(data_point)
        
        return simulated_data
    
    def _calculate_mvrv_ratio(self, market_cap: float, realized_cap: float) -> float:
        """Calculate MVRV (Market Value to Realized Value) ratio."""
        try:
            if realized_cap and realized_cap > 0:
                return market_cap / realized_cap
        except (TypeError, ZeroDivisionError):
            pass
        return 0.0
    
    def _generate_network_insights(self, summary: Dict[str, Any]) -> str:
        """Generate insights based on network data."""
        insights = []
        
        # Analyze network health
        for asset, health in summary.get("network_health", {}).items():
            active_addresses = health.get("active_addresses", 0)
            tx_count = health.get("transaction_count", 0)
            
            if active_addresses > 800000:
                insights.append(f"{asset.upper()} network highly active ({active_addresses:,} addresses)")
            elif active_addresses > 500000:
                insights.append(f"{asset.upper()} network moderately active")
            
            if tx_count > 300000:
                insights.append(f"{asset.upper()} high transaction volume")
        
        # Analyze market metrics
        total_market_cap = sum(
            data.get("market_cap_usd", 0) 
            for data in summary.get("market_metrics", {}).values()
        )
        
        if total_market_cap > 2000000000000:  # $2T
            insights.append("Combined market cap exceeds $2T")
        elif total_market_cap > 1000000000000:  # $1T
            insights.append("Strong combined market cap above $1T")
        
        return " | ".join(insights) if insights else "Standard network activity levels"
    
    def _generate_supply_insights(self, supply_data: Dict[str, Any]) -> str:
        """Generate insights based on supply data."""
        insights = []
        
        # Analyze MVRV ratios
        for asset, data in supply_data.get("assets", {}).items():
            mvrv = data.get("mvrv_ratio", 0)
            
            if mvrv > 3:
                insights.append(f"{asset.upper()} potentially overvalued (MVRV: {mvrv:.1f})")
            elif mvrv < 1:
                insights.append(f"{asset.upper()} potentially undervalued (MVRV: {mvrv:.1f})")
            elif 1 <= mvrv <= 2:
                insights.append(f"{asset.upper()} fair value range")
        
        # Analyze total market cap
        total_cap = supply_data.get("total_market_cap", 0)
        if total_cap > 2000000000000:
            insights.append("Massive combined market capitalization")
        
        return " | ".join(insights) if insights else "Standard supply metrics"
    
    def _format_timestamp(self, timestamp) -> str:
        """Format timestamp to readable string."""
        try:
            if timestamp:
                if isinstance(timestamp, str):
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                else:
                    dt = datetime.fromtimestamp(timestamp)
                return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            pass
        return "Unknown"
    
    def _is_cache_valid(self) -> bool:
        """Check if cache is still valid."""
        if self._cache_timestamp is None:
            return False
        
        return (datetime.now() - self._cache_timestamp).total_seconds() < self._cache_ttl
