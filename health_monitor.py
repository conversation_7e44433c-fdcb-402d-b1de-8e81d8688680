"""
Internal Health Monitor for tracking system performance and scraper health.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog
import psutil
import platform
import sys
from pathlib import Path

from alpha_grid.agents.scrapers.base_scraper import BaseScraper
from alpha_grid.core.config import config_manager


logger = structlog.get_logger(__name__)


class HealthMonitor(BaseScraper):
    """Internal health monitor for system and scraper performance tracking."""
    
    def __init__(self):
        """Initialize Health Monitor."""
        super().__init__(
            name="HealthMonitor",
            rate_limit=60,  # 1 request per second for internal monitoring
            api_key=None,   # No external API required
            base_url=None   # Internal monitoring only
        )
        
        # Health tracking
        self._health_history = []
        self._max_history = 100  # Keep last 100 health checks
        self._start_time = datetime.now()
        
        # System info cache
        self._system_info = None
        self._system_info_timestamp = None
        self._system_info_ttl = 3600  # 1 hour
        
        self.logger = logger.bind(scraper="HealthMonitor")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers (none required for internal monitoring)."""
        return {}
    
    async def health_check(self) -> bool:
        """Check if Health Monitor is healthy (always true for internal monitor)."""
        return True
    
    async def get_system_health(self) -> Dict[str, Any]:
        """
        Get comprehensive system health metrics.
        
        Returns:
            System health data
        """
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()

            # CPU frequency (may not be available on all systems)
            try:
                cpu_freq = psutil.cpu_freq()
            except (OSError, AttributeError):
                cpu_freq = None
            
            # Memory metrics
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            
            # Network metrics (basic)
            network = psutil.net_io_counters()
            
            # Process metrics
            process = psutil.Process()
            process_memory = process.memory_info()
            process_cpu = process.cpu_percent()
            
            system_health = {
                "timestamp": datetime.now().isoformat(),
                "uptime_seconds": (datetime.now() - self._start_time).total_seconds(),
                "cpu": {
                    "usage_percent": cpu_percent,
                    "count": cpu_count,
                    "frequency_mhz": cpu_freq.current if cpu_freq else None,
                    "load_average": self._get_load_average()
                },
                "memory": {
                    "total_gb": memory.total / (1024**3),
                    "available_gb": memory.available / (1024**3),
                    "used_gb": memory.used / (1024**3),
                    "usage_percent": memory.percent,
                    "swap_total_gb": swap.total / (1024**3),
                    "swap_used_gb": swap.used / (1024**3),
                    "swap_percent": swap.percent
                },
                "disk": {
                    "total_gb": disk.total / (1024**3),
                    "used_gb": disk.used / (1024**3),
                    "free_gb": disk.free / (1024**3),
                    "usage_percent": (disk.used / disk.total) * 100
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                },
                "process": {
                    "memory_mb": process_memory.rss / (1024**2),
                    "cpu_percent": process_cpu,
                    "threads": process.num_threads(),
                    "open_files": self._get_open_files_count(process)
                },
                "health_status": self._assess_system_health(cpu_percent, memory.percent, disk.used / disk.total * 100)
            }
            
            self.logger.info("System health metrics collected")
            return system_health
            
        except Exception as e:
            self.logger.error("Failed to get system health", error=str(e))
            raise
    
    async def get_scraper_health(self, scrapers: Optional[List[Any]] = None) -> Dict[str, Any]:
        """
        Get health status of all scrapers.
        
        Args:
            scrapers: List of scraper instances to check
            
        Returns:
            Scraper health data
        """
        try:
            if scrapers is None:
                scrapers = []
            
            scraper_health = {
                "timestamp": datetime.now().isoformat(),
                "total_scrapers": len(scrapers),
                "scrapers": [],
                "overall_health": "unknown"
            }
            
            healthy_count = 0
            total_requests = 0
            total_errors = 0
            
            for scraper in scrapers:
                try:
                    # Get scraper metrics
                    metrics = await scraper.get_metrics()
                    
                    # Perform health check
                    is_healthy = await scraper.health_check()
                    
                    scraper_info = {
                        "name": metrics.get("name", "Unknown"),
                        "is_healthy": is_healthy,
                        "request_count": metrics.get("request_count", 0),
                        "error_count": metrics.get("error_count", 0),
                        "success_rate": self._calculate_success_rate(
                            metrics.get("request_count", 0),
                            metrics.get("error_count", 0)
                        ),
                        "rate_limit": metrics.get("rate_limit", 0),
                        "last_request": metrics.get("last_request_time", "Never"),
                        "status": "healthy" if is_healthy else "unhealthy"
                    }
                    
                    scraper_health["scrapers"].append(scraper_info)
                    
                    if is_healthy:
                        healthy_count += 1
                    
                    total_requests += metrics.get("request_count", 0)
                    total_errors += metrics.get("error_count", 0)
                    
                except Exception as e:
                    self.logger.warning("Failed to check scraper health", scraper=str(scraper), error=str(e))
                    scraper_health["scrapers"].append({
                        "name": str(scraper),
                        "is_healthy": False,
                        "status": "error",
                        "error": str(e)
                    })
            
            # Calculate overall health
            if len(scrapers) == 0:
                overall_health = "no_scrapers"
            elif healthy_count == len(scrapers):
                overall_health = "healthy"
            elif healthy_count >= len(scrapers) * 0.8:
                overall_health = "healthy"
            elif healthy_count >= len(scrapers) * 0.5:
                overall_health = "fair"
            else:
                overall_health = "poor"
            
            scraper_health.update({
                "healthy_scrapers": healthy_count,
                "unhealthy_scrapers": len(scrapers) - healthy_count,
                "overall_health": overall_health,
                "total_requests": total_requests,
                "total_errors": total_errors,
                "overall_success_rate": self._calculate_success_rate(total_requests, total_errors)
            })
            
            self.logger.info("Scraper health metrics collected", healthy=healthy_count, total=len(scrapers))
            return scraper_health
            
        except Exception as e:
            self.logger.error("Failed to get scraper health", error=str(e))
            raise
    
    async def get_system_info(self) -> Dict[str, Any]:
        """
        Get system information (cached for performance).
        
        Returns:
            System information data
        """
        try:
            # Check cache
            if (self._system_info and self._system_info_timestamp and 
                (datetime.now() - self._system_info_timestamp).total_seconds() < self._system_info_ttl):
                return self._system_info
            
            # Get fresh system info
            system_info = {
                "platform": {
                    "system": platform.system(),
                    "release": platform.release(),
                    "version": platform.version(),
                    "machine": platform.machine(),
                    "processor": platform.processor(),
                    "architecture": platform.architecture()[0]
                },
                "python": {
                    "version": sys.version,
                    "executable": sys.executable,
                    "path": sys.path[:3]  # First 3 paths only
                },
                "environment": {
                    "working_directory": str(Path.cwd()),
                    "user": psutil.users()[0].name if psutil.users() else "unknown",
                    "boot_time": datetime.fromtimestamp(psutil.boot_time()).isoformat()
                },
                "alpha_grid": {
                    "start_time": self._start_time.isoformat(),
                    "config_file": str(config_manager.config_file) if hasattr(config_manager, 'config_file') else "unknown",
                    "log_level": "INFO"  # Default log level
                }
            }
            
            # Cache the result
            self._system_info = system_info
            self._system_info_timestamp = datetime.now()
            
            self.logger.info("System information collected")
            return system_info
            
        except Exception as e:
            self.logger.error("Failed to get system info", error=str(e))
            raise
    
    async def get_health_summary(self, scrapers: Optional[List[Any]] = None) -> Dict[str, Any]:
        """
        Get comprehensive health summary.
        
        Args:
            scrapers: List of scraper instances to check
            
        Returns:
            Complete health summary
        """
        try:
            # Get all health data
            system_health = await self.get_system_health()
            scraper_health = await self.get_scraper_health(scrapers)
            system_info = await self.get_system_info()
            
            # Calculate overall system status
            overall_status = self._determine_overall_status(system_health, scraper_health)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(system_health, scraper_health)
            
            summary = {
                "timestamp": datetime.now().isoformat(),
                "overall_status": overall_status,
                "system_health": system_health,
                "scraper_health": scraper_health,
                "system_info": system_info,
                "recommendations": recommendations,
                "uptime": {
                    "seconds": (datetime.now() - self._start_time).total_seconds(),
                    "human_readable": self._format_uptime((datetime.now() - self._start_time).total_seconds())
                }
            }
            
            # Store in history
            self._health_history.append({
                "timestamp": datetime.now().isoformat(),
                "overall_status": overall_status,
                "cpu_percent": system_health["cpu"]["usage_percent"],
                "memory_percent": system_health["memory"]["usage_percent"],
                "healthy_scrapers": scraper_health["healthy_scrapers"],
                "total_scrapers": scraper_health["total_scrapers"]
            })
            
            # Trim history
            if len(self._health_history) > self._max_history:
                self._health_history = self._health_history[-self._max_history:]
            
            self.logger.info("Health summary generated", status=overall_status)
            return summary
            
        except Exception as e:
            self.logger.error("Failed to generate health summary", error=str(e))
            raise
    
    async def get_health_history(self) -> List[Dict[str, Any]]:
        """
        Get health check history.
        
        Returns:
            List of historical health data
        """
        return self._health_history.copy()
    
    def _calculate_success_rate(self, requests: int, errors: int) -> float:
        """Calculate success rate percentage."""
        if requests == 0:
            return 100.0
        return ((requests - errors) / requests) * 100
    
    def _assess_system_health(self, cpu_percent: float, memory_percent: float, disk_percent: float) -> str:
        """Assess overall system health based on metrics."""
        if cpu_percent > 90 or memory_percent > 90 or disk_percent > 95:
            return "critical"
        elif cpu_percent > 80 or memory_percent > 80 or disk_percent > 90:
            return "warning"
        elif cpu_percent > 70 or memory_percent > 70 or disk_percent > 85:
            return "fair"
        else:
            return "healthy"
    
    def _determine_overall_status(self, system_health: Dict, scraper_health: Dict) -> str:
        """Determine overall system status."""
        system_status = system_health.get("health_status", "unknown")
        scraper_status = scraper_health.get("overall_health", "unknown")
        
        # Map statuses to numeric values for comparison
        status_values = {
            "excellent": 5, "good": 4, "fair": 3, "warning": 2, "poor": 1, "critical": 0, "unknown": 0
        }
        
        system_value = status_values.get(system_status, 0)
        scraper_value = status_values.get(scraper_status, 0)
        
        # Take the minimum (worst) status
        min_value = min(system_value, scraper_value)
        
        # Convert back to status
        for status, value in status_values.items():
            if value == min_value:
                return status
        
        return "unknown"
    
    def _generate_recommendations(self, system_health: Dict, scraper_health: Dict) -> List[str]:
        """Generate health recommendations."""
        recommendations = []
        
        # System recommendations
        cpu_percent = system_health.get("cpu", {}).get("usage_percent", 0)
        memory_percent = system_health.get("memory", {}).get("usage_percent", 0)
        disk_percent = system_health.get("disk", {}).get("usage_percent", 0)
        
        if cpu_percent > 80:
            recommendations.append("High CPU usage detected - consider reducing scraper frequency")
        
        if memory_percent > 80:
            recommendations.append("High memory usage detected - check for memory leaks")
        
        if disk_percent > 90:
            recommendations.append("Low disk space - clean up logs and temporary files")
        
        # Scraper recommendations
        unhealthy_scrapers = scraper_health.get("unhealthy_scrapers", 0)
        total_scrapers = scraper_health.get("total_scrapers", 0)
        
        if unhealthy_scrapers > 0:
            recommendations.append(f"{unhealthy_scrapers} scrapers are unhealthy - check API keys and endpoints")
        
        if total_scrapers == 0:
            recommendations.append("No scrapers configured - add data sources")
        
        overall_success_rate = scraper_health.get("overall_success_rate", 100)
        if overall_success_rate < 90:
            recommendations.append("Low success rate - check network connectivity and rate limits")
        
        if not recommendations:
            recommendations.append("System is running optimally")
        
        return recommendations
    
    def _get_load_average(self) -> Optional[List[float]]:
        """Get system load average if available."""
        try:
            if hasattr(psutil, 'getloadavg'):
                return list(psutil.getloadavg())
        except (OSError, AttributeError):
            pass
        return None

    def _get_open_files_count(self, process) -> Optional[int]:
        """Get count of open files for process if available."""
        try:
            if hasattr(process, 'open_files'):
                return len(process.open_files())
        except (OSError, AttributeError, psutil.AccessDenied):
            pass
        return None

    def _format_uptime(self, seconds: float) -> str:
        """Format uptime in human readable format."""
        days = int(seconds // 86400)
        hours = int((seconds % 86400) // 3600)
        minutes = int((seconds % 3600) // 60)

        if days > 0:
            return f"{days}d {hours}h {minutes}m"
        elif hours > 0:
            return f"{hours}h {minutes}m"
        else:
            return f"{minutes}m"
