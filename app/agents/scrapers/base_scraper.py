"""
Enhanced base scraper class with rate limiting, circuit breaker, and retry logic.
Includes Polars integration for high-performance data processing.
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, List, Union
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
import httpx
import polars as pl
import structlog
from tenacity import retry, stop_after_attempt, wait_exponential, before_sleep_log

from ...core.config import settings
from ...core.circuit_breaker import get_circuit_breaker, CircuitBreakerConfig, CircuitBreakerOpenError
from ...core.logging import LoggerMixin

logger = structlog.get_logger(__name__)


class TokenBucket:
    """Token bucket rate limiter implementation with async support."""
    
    def __init__(self, rate: int, capacity: Optional[int] = None):
        """
        Initialize token bucket.
        
        Args:
            rate: Tokens per minute
            capacity: Maximum tokens (defaults to rate)
        """
        self.rate = rate / 60.0  # Convert to tokens per second
        self.capacity = capacity or rate
        self.tokens = self.capacity
        self.last_update = time.time()
        self._lock = asyncio.Lock()
    
    async def acquire(self, tokens: int = 1) -> bool:
        """Acquire tokens from bucket."""
        async with self._lock:
            now = time.time()
            # Add tokens based on elapsed time
            elapsed = now - self.last_update
            self.tokens = min(self.capacity, self.tokens + elapsed * self.rate)
            self.last_update = now
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False
    
    async def wait_for_tokens(self, tokens: int = 1):
        """Wait until tokens are available."""
        while not await self.acquire(tokens):
            await asyncio.sleep(0.1)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current token bucket status."""
        return {
            "tokens_available": self.tokens,
            "capacity": self.capacity,
            "rate_per_minute": self.rate * 60,
            "last_update": self.last_update
        }


class BaseScraper(ABC, LoggerMixin):
    """Enhanced base class for all API scrapers with reliability features."""
    
    def __init__(
        self,
        name: str,
        rate_limit: int,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None
    ):
        """
        Initialize base scraper.
        
        Args:
            name: Scraper name for logging
            rate_limit: Requests per minute
            api_key: API key if required
            base_url: Base URL for API
        """
        self.name = name
        self.rate_limit = rate_limit
        self.api_key = api_key
        self.base_url = base_url
        
        # Rate limiting
        self.token_bucket = TokenBucket(rate_limit)
        
        # HTTP client with connection pooling
        self.client = httpx.AsyncClient(
            timeout=settings.request_timeout,
            headers=self._get_default_headers(),
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
        )
        
        # Enhanced circuit breaker
        cb_config = CircuitBreakerConfig(
            failure_threshold=settings.failure_threshold,
            recovery_timeout=settings.recovery_timeout,
            timeout=settings.request_timeout,
            expected_exceptions=(httpx.HTTPError, httpx.TimeoutException, ConnectionError)
        )
        self.circuit_breaker = get_circuit_breaker(f"{name}_api", cb_config)
        
        # Metrics tracking
        self.metrics = {
            "request_count": 0,
            "error_count": 0,
            "success_count": 0,
            "last_request_time": 0,
            "total_response_time": 0.0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        
        # Data caching
        self._cache: Dict[str, Any] = {}
        self._cache_timestamps: Dict[str, float] = {}
        self._cache_ttl = 300  # 5 minutes default
        
        # Initialize logger with scraper context
        self._scraper_logger = logger.bind(scraper=name)
    
    def _get_default_headers(self) -> Dict[str, str]:
        """Get default HTTP headers."""
        return {
            "User-Agent": f"Alpha-Grid/{settings.app_version}",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, max=30),
        before_sleep=before_sleep_log(logger, logging.WARNING)
    )
    async def _make_request(
        self,
        url: str,
        method: str = "GET",
        params: Optional[Dict] = None,
        headers: Optional[Dict] = None,
        **kwargs
    ) -> Dict[Any, Any]:
        """Make HTTP request with retry logic and enhanced error handling."""
        
        # Wait for rate limit
        await self.token_bucket.wait_for_tokens()
        
        # Prepare request
        request_headers = self._get_default_headers()
        if headers:
            request_headers.update(headers)
        
        # Add API key if required
        if self.api_key:
            request_headers.update(self._get_auth_headers())
        
        start_time = time.time()
        
        self._scraper_logger.debug(
            "Making request",
            url=url,
            method=method,
            params=params
        )
        
        try:
            # Make request through circuit breaker
            response = await self.circuit_breaker.call(
                self.client.request,
                method,
                url,
                params=params,
                headers=request_headers,
                **kwargs
            )
            
            response.raise_for_status()
            
            # Update metrics
            response_time = time.time() - start_time
            self.metrics["request_count"] += 1
            self.metrics["success_count"] += 1
            self.metrics["last_request_time"] = time.time()
            self.metrics["total_response_time"] += response_time
            
            # Parse JSON response
            data = response.json()
            
            self._scraper_logger.debug(
                "Request successful",
                status_code=response.status_code,
                response_time_ms=round(response_time * 1000, 2),
                response_size=len(str(data))
            )
            
            return data
            
        except CircuitBreakerOpenError as e:
            self.metrics["error_count"] += 1
            self._scraper_logger.warning(
                "Request blocked by circuit breaker",
                url=url,
                circuit_state=self.circuit_breaker.state.value
            )
            raise

        except httpx.TimeoutException as e:
            self.metrics["error_count"] += 1
            self._scraper_logger.warning(
                "Request timed out",
                url=url,
                timeout=settings.request_timeout
            )
            raise

        except httpx.HTTPStatusError as e:
            self.metrics["error_count"] += 1
            self._scraper_logger.warning(
                "HTTP error response",
                url=url,
                status_code=e.response.status_code,
                response_text=e.response.text[:200]
            )
            raise

        except httpx.RequestError as e:
            self.metrics["error_count"] += 1
            self._scraper_logger.error(
                "Request error",
                url=url,
                error=str(e),
                error_type=type(e).__name__
            )
            raise

        except Exception as e:
            self.metrics["error_count"] += 1
            self._scraper_logger.error(
                "Unexpected error",
                error=str(e),
                url=url,
                error_type=type(e).__name__
            )
            raise
    
    def _get_cache_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments."""
        import hashlib
        key_data = f"{args}_{kwargs}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid."""
        if cache_key not in self._cache_timestamps:
            return False
        
        age = time.time() - self._cache_timestamps[cache_key]
        return age < self._cache_ttl
    
    def _get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get data from cache if valid."""
        if self._is_cache_valid(cache_key):
            self.metrics["cache_hits"] += 1
            return self._cache.get(cache_key)
        
        self.metrics["cache_misses"] += 1
        return None
    
    def _cache_data(self, cache_key: str, data: Any) -> None:
        """Cache data with timestamp."""
        self._cache[cache_key] = data
        self._cache_timestamps[cache_key] = time.time()
    
    def process_data_with_polars(self, data: List[Dict[str, Any]]) -> pl.DataFrame:
        """Process data using Polars for high performance."""
        try:
            if not data:
                return pl.DataFrame()
            
            # Convert to Polars DataFrame
            df = pl.DataFrame(data)
            
            # Basic data cleaning
            df = df.drop_nulls()
            
            # Add processing timestamp
            df = df.with_columns(
                pl.lit(datetime.now().isoformat()).alias("processed_at")
            )
            
            self._scraper_logger.debug(
                "Data processed with Polars",
                rows=df.height,
                columns=df.width
            )
            
            return df
            
        except Exception as e:
            self._scraper_logger.error(
                "Failed to process data with Polars",
                error=str(e),
                data_length=len(data)
            )
            # Fallback to empty DataFrame
            return pl.DataFrame()
    
    @abstractmethod
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers. Override in subclasses."""
        return {}
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if the API is healthy. Override in subclasses."""
        pass
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get comprehensive scraper metrics."""
        avg_response_time = (
            self.metrics["total_response_time"] / max(self.metrics["success_count"], 1)
        )
        
        return {
            "name": self.name,
            "request_count": self.metrics["request_count"],
            "success_count": self.metrics["success_count"],
            "error_count": self.metrics["error_count"],
            "success_rate": self.metrics["success_count"] / max(self.metrics["request_count"], 1),
            "error_rate": self.metrics["error_count"] / max(self.metrics["request_count"], 1),
            "avg_response_time_ms": round(avg_response_time * 1000, 2),
            "last_request_time": self.metrics["last_request_time"],
            "circuit_breaker_state": self.circuit_breaker.state.value,
            "rate_limit": self.rate_limit,
            "token_bucket_status": self.token_bucket.get_status(),
            "cache_hit_rate": self.metrics["cache_hits"] / max(
                self.metrics["cache_hits"] + self.metrics["cache_misses"], 1
            )
        }
    
    async def clear_cache(self) -> None:
        """Clear all cached data."""
        self._cache.clear()
        self._cache_timestamps.clear()
        self._scraper_logger.info("Cache cleared")
    
    async def close(self):
        """Close HTTP client and cleanup resources."""
        await self.client.aclose()
        self._scraper_logger.info("Scraper closed")
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()