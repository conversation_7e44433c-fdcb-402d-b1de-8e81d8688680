"""
Etherscan API scraper for Ethereum blockchain data.
Enhanced with proper error handling and rate limiting.
"""

import time
from typing import Dict, List, Any, Optional
from datetime import datetime
import structlog

from .base_scraper import BaseScraper
from ...core.config import settings

logger = structlog.get_logger(__name__)


class EtherscanScraper(BaseScraper):
    """Etherscan API scraper for Ethereum blockchain data."""
    
    def __init__(self):
        """Initialize Etherscan scraper."""
        api_key = settings.etherscan_api_key
        super().__init__(
            name="Etherscan",
            rate_limit=settings.etherscan_rate_limit,
            api_key=api_key,
            base_url="https://api.etherscan.io/api"
        )
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Etherscan uses API key in query params, not headers."""
        return {}
    
    def _get_base_params(self) -> Dict[str, str]:
        """Get base parameters including API key."""
        params = {}
        if self.api_key:
            params["apikey"] = self.api_key
        return params
    
    async def health_check(self) -> bool:
        """Check if Etherscan API is healthy."""
        try:
            # Test with a simple balance query for a known address
            params = self._get_base_params()
            params.update({
                "module": "account",
                "action": "balance",
                "address": "******************************************",  # Ethereum Foundation
                "tag": "latest"
            })
            
            response = await self._make_request(self.base_url, params=params)
            return response.get("status") == "1"
        except Exception as e:
            self.logger.error("Health check failed", error=str(e))
            return False
    
    async def get_account_balance(self, address: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        Get ETH balance for an address.
        
        Args:
            address: Ethereum address
            use_cache: Whether to use cached data
            
        Returns:
            Account balance information
        """
        try:
            # Check cache
            cache_key = self._get_cache_key("balance", address)
            if use_cache:
                cached_data = self._get_cached_data(cache_key)
                if cached_data is not None:
                    return cached_data
            
            params = self._get_base_params()
            params.update({
                "module": "account",
                "action": "balance",
                "address": address,
                "tag": "latest"
            })
            
            self.logger.debug("Fetching account balance", address=address)
            
            response = await self._make_request(self.base_url, params=params)
            
            if response.get("status") != "1":
                raise ValueError(f"API error: {response.get('message', 'Unknown error')}")
            
            # Convert wei to ETH
            balance_wei = int(response.get("result", "0"))
            balance_eth = balance_wei / 10**18
            
            result = {
                "address": address,
                "balance_wei": balance_wei,
                "balance_eth": balance_eth,
                "data_source": "etherscan",
                "fetched_at": datetime.now().isoformat()
            }
            
            # Cache the result
            if use_cache:
                self._cache_data(cache_key, result)
            
            self.logger.debug("Account balance fetched", address=address, balance_eth=balance_eth)
            return result
            
        except Exception as e:
            self.logger.error("Failed to get account balance", error=str(e), address=address)
            raise
    
    async def get_transaction_history(
        self, 
        address: str, 
        start_block: int = 0,
        end_block: int = ********,
        page: int = 1,
        offset: int = 100,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Get transaction history for an address.
        
        Args:
            address: Ethereum address
            start_block: Starting block number
            end_block: Ending block number
            page: Page number
            offset: Number of transactions per page (max 10000)
            use_cache: Whether to use cached data
            
        Returns:
            List of transactions
        """
        try:
            # Check cache
            cache_key = self._get_cache_key("txlist", address, start_block, end_block, page, offset)
            if use_cache:
                cached_data = self._get_cached_data(cache_key)
                if cached_data is not None:
                    return cached_data
            
            params = self._get_base_params()
            params.update({
                "module": "account",
                "action": "txlist",
                "address": address,
                "startblock": start_block,
                "endblock": end_block,
                "page": page,
                "offset": min(offset, 10000),  # API limit
                "sort": "desc"
            })
            
            self.logger.debug(
                "Fetching transaction history",
                address=address,
                page=page,
                offset=offset
            )
            
            response = await self._make_request(self.base_url, params=params)
            
            if response.get("status") != "1":
                if response.get("message") == "No transactions found":
                    return []
                raise ValueError(f"API error: {response.get('message', 'Unknown error')}")
            
            transactions = response.get("result", [])
            
            # Process transactions
            processed_txs = []
            for tx in transactions:
                processed_tx = {
                    "hash": tx.get("hash"),
                    "block_number": int(tx.get("blockNumber", 0)),
                    "timestamp": int(tx.get("timeStamp", 0)),
                    "from_address": tx.get("from"),
                    "to_address": tx.get("to"),
                    "value_wei": int(tx.get("value", 0)),
                    "value_eth": int(tx.get("value", 0)) / 10**18,
                    "gas": int(tx.get("gas", 0)),
                    "gas_price": int(tx.get("gasPrice", 0)),
                    "gas_used": int(tx.get("gasUsed", 0)),
                    "transaction_fee_eth": (int(tx.get("gasUsed", 0)) * int(tx.get("gasPrice", 0))) / 10**18,
                    "is_error": tx.get("isError") == "1",
                    "confirmation_status": tx.get("confirmations"),
                    "data_source": "etherscan",
                    "fetched_at": datetime.now().isoformat()
                }
                processed_txs.append(processed_tx)
            
            # Cache the result
            if use_cache:
                self._cache_data(cache_key, processed_txs)
            
            self.logger.info(
                "Transaction history fetched",
                address=address,
                transaction_count=len(processed_txs)
            )
            
            return processed_txs
            
        except Exception as e:
            self.logger.error(
                "Failed to get transaction history",
                error=str(e),
                address=address
            )
            raise
    
    async def get_token_balance(
        self, 
        address: str, 
        contract_address: str,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Get ERC-20 token balance for an address.
        
        Args:
            address: Wallet address
            contract_address: Token contract address
            use_cache: Whether to use cached data
            
        Returns:
            Token balance information
        """
        try:
            # Check cache
            cache_key = self._get_cache_key("tokenbalance", address, contract_address)
            if use_cache:
                cached_data = self._get_cached_data(cache_key)
                if cached_data is not None:
                    return cached_data
            
            params = self._get_base_params()
            params.update({
                "module": "account",
                "action": "tokenbalance",
                "contractaddress": contract_address,
                "address": address,
                "tag": "latest"
            })
            
            self.logger.debug(
                "Fetching token balance",
                address=address,
                contract=contract_address
            )
            
            response = await self._make_request(self.base_url, params=params)
            
            if response.get("status") != "1":
                raise ValueError(f"API error: {response.get('message', 'Unknown error')}")
            
            balance_raw = int(response.get("result", "0"))
            
            result = {
                "address": address,
                "contract_address": contract_address,
                "balance_raw": balance_raw,
                "data_source": "etherscan",
                "fetched_at": datetime.now().isoformat()
            }
            
            # Cache the result
            if use_cache:
                self._cache_data(cache_key, result)
            
            self.logger.debug(
                "Token balance fetched",
                address=address,
                contract=contract_address,
                balance=balance_raw
            )
            
            return result
            
        except Exception as e:
            self.logger.error(
                "Failed to get token balance",
                error=str(e),
                address=address,
                contract=contract_address
            )
            raise
    
    async def get_gas_price(self, use_cache: bool = True) -> Dict[str, Any]:
        """
        Get current gas price.
        
        Args:
            use_cache: Whether to use cached data
            
        Returns:
            Gas price information
        """
        try:
            # Check cache with shorter TTL for gas prices
            cache_key = self._get_cache_key("gasprice")
            if use_cache:
                cached_data = self._get_cached_data(cache_key)
                if cached_data is not None:
                    return cached_data
            
            params = self._get_base_params()
            params.update({
                "module": "gastracker",
                "action": "gasoracle"
            })
            
            self.logger.debug("Fetching gas price")
            
            response = await self._make_request(self.base_url, params=params)
            
            if response.get("status") != "1":
                raise ValueError(f"API error: {response.get('message', 'Unknown error')}")
            
            result_data = response.get("result", {})
            
            result = {
                "safe_gas_price": int(result_data.get("SafeGasPrice", 0)),
                "standard_gas_price": int(result_data.get("StandardGasPrice", 0)),
                "fast_gas_price": int(result_data.get("FastGasPrice", 0)),
                "data_source": "etherscan",
                "fetched_at": datetime.now().isoformat()
            }
            
            # Cache with shorter TTL (1 minute for gas prices)
            if use_cache:
                self._cache_data(cache_key, result)
                # Override cache timestamp for shorter TTL
                self._cache_timestamps[cache_key] = time.time() - (self._cache_ttl - 60)
            
            self.logger.debug("Gas price fetched", **result)
            return result
            
        except Exception as e:
            self.logger.error("Failed to get gas price", error=str(e))
            raise