"""
Scraper registry for managing and monitoring all data scrapers.
Provides centralized configuration, health monitoring, and failover capabilities.
"""

import asyncio
from typing import Dict, List, Any, Optional, Type, Set
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass, field

from .base_scraper import BaseScraper
from ...core.logging import LoggerMixin
from ...core.config import settings

logger = structlog.get_logger(__name__)


@dataclass
class ScraperInfo:
    """Information about a registered scraper."""
    name: str
    scraper_class: Type[BaseScraper]
    instance: Optional[BaseScraper] = None
    is_enabled: bool = True
    priority: int = 1  # Higher number = higher priority
    tags: Set[str] = field(default_factory=set)
    last_health_check: Optional[datetime] = None
    consecutive_failures: int = 0
    max_failures: int = 5


class ScraperRegistry(LoggerMixin):
    """Registry for managing multiple scrapers with health monitoring and failover."""

    def __init__(self):
        self._scrapers: Dict[str, ScraperInfo] = {}
        self._running_scrapers: Dict[str, BaseScraper] = {}
        self._health_check_interval = 300  # 5 minutes
        self._health_check_task: Optional[asyncio.Task] = None
        self._is_monitoring = False

        self._logger = logger.bind(component="scraper_registry")
    
    def register_scraper(
        self,
        name: str,
        scraper_class: Type[BaseScraper],
        priority: int = 1,
        tags: Optional[Set[str]] = None,
        enabled: bool = True
    ) -> None:
        """
        Register a scraper class.
        
        Args:
            name: Unique scraper name
            scraper_class: Scraper class (not instance)
            priority: Priority level (higher = more important)
            tags: Tags for categorization
            enabled: Whether scraper is enabled
        """
        if name in self._scrapers:
            self._logger.warning("Scraper already registered, updating", name=name)

        scraper_info = ScraperInfo(
            name=name,
            scraper_class=scraper_class,
            priority=priority,
            tags=tags or set(),
            is_enabled=enabled
        )

        self._scrapers[name] = scraper_info
        self._logger.info("Scraper registered", name=name, priority=priority, tags=list(tags or []))
    
    def unregister_scraper(self, name: str) -> bool:
        """Unregister a scraper."""
        if name not in self._scrapers:
            return False
        
        # Stop if running
        if name in self._running_scrapers:
            asyncio.create_task(self.stop_scraper(name))
        
        del self._scrapers[name]
        self._logger.info("Scraper unregistered", name=name)
        return True
    
    async def start_scraper(self, name: str, **kwargs) -> bool:
        """
        Start a specific scraper.
        
        Args:
            name: Scraper name
            **kwargs: Arguments to pass to scraper constructor
            
        Returns:
            True if started successfully
        """
        if name not in self._scrapers:
            self._logger.error("Scraper not registered", name=name)
            return False
        
        scraper_info = self._scrapers[name]
        
        if not scraper_info.is_enabled:
            self._logger.warning("Scraper is disabled", name=name)
            return False
        
        if name in self._running_scrapers:
            self._logger.warning("Scraper already running", name=name)
            return True
        
        try:
            # Create scraper instance
            instance = scraper_info.scraper_class(**kwargs)
            scraper_info.instance = instance
            self._running_scrapers[name] = instance
            
            # Perform initial health check
            is_healthy = await instance.health_check()
            if not is_healthy:
                self._logger.warning("Scraper failed initial health check", name=name)
                await self.stop_scraper(name)
                return False
            
            scraper_info.last_health_check = datetime.now()
            scraper_info.consecutive_failures = 0
            
            self._logger.info("Scraper started successfully", name=name)
            return True
            
        except Exception as e:
            self._logger.error("Failed to start scraper", name=name, error=str(e))
            scraper_info.consecutive_failures += 1
            return False
    
    async def stop_scraper(self, name: str) -> bool:
        """Stop a specific scraper."""
        if name not in self._running_scrapers:
            return False
        
        try:
            scraper = self._running_scrapers[name]
            await scraper.close()
            
            del self._running_scrapers[name]
            if name in self._scrapers:
                self._scrapers[name].instance = None
            
            self._logger.info("Scraper stopped", name=name)
            return True
            
        except Exception as e:
            self._logger.error("Error stopping scraper", name=name, error=str(e))
            return False
    
    async def start_all_scrapers(self, **kwargs) -> Dict[str, bool]:
        """Start all enabled scrapers."""
        results = {}
        
        # Sort by priority (highest first)
        sorted_scrapers = sorted(
            self._scrapers.items(),
            key=lambda x: x[1].priority,
            reverse=True
        )
        
        for name, scraper_info in sorted_scrapers:
            if scraper_info.is_enabled:
                results[name] = await self.start_scraper(name, **kwargs)
                # Small delay between starts to avoid overwhelming APIs
                await asyncio.sleep(1)
        
        self._logger.info("Started all scrapers", results=results)
        return results
    
    async def stop_all_scrapers(self) -> Dict[str, bool]:
        """Stop all running scrapers."""
        results = {}
        
        for name in list(self._running_scrapers.keys()):
            results[name] = await self.stop_scraper(name)
        
        self._logger.info("Stopped all scrapers", results=results)
        return results
    
    def get_scraper(self, name: str) -> Optional[BaseScraper]:
        """Get a running scraper instance."""
        return self._running_scrapers.get(name)
    
    def get_scrapers_by_tag(self, tag: str) -> List[BaseScraper]:
        """Get all running scrapers with a specific tag."""
        scrapers = []
        
        for name, scraper_info in self._scrapers.items():
            if tag in scraper_info.tags and name in self._running_scrapers:
                scrapers.append(self._running_scrapers[name])
        
        return scrapers
    
    def list_scrapers(self) -> Dict[str, Dict[str, Any]]:
        """List all registered scrapers with their status."""
        result = {}
        
        for name, scraper_info in self._scrapers.items():
            result[name] = {
                "name": name,
                "class": scraper_info.scraper_class.__name__,
                "is_enabled": scraper_info.is_enabled,
                "is_running": name in self._running_scrapers,
                "priority": scraper_info.priority,
                "tags": list(scraper_info.tags),
                "last_health_check": scraper_info.last_health_check.isoformat() if scraper_info.last_health_check else None,
                "consecutive_failures": scraper_info.consecutive_failures
            }
        
        return result
    
    async def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get metrics from all running scrapers."""
        metrics = {}
        
        for name, scraper in self._running_scrapers.items():
            try:
                metrics[name] = await scraper.get_metrics()
            except Exception as e:
                self._logger.error("Failed to get metrics", scraper=name, error=str(e))
                metrics[name] = {"error": str(e)}
        
        return metrics
    
    async def health_check_all(self) -> Dict[str, bool]:
        """Perform health check on all running scrapers."""
        results = {}
        
        for name, scraper in self._running_scrapers.items():
            try:
                is_healthy = await scraper.health_check()
                results[name] = is_healthy
                
                scraper_info = self._scrapers[name]
                scraper_info.last_health_check = datetime.now()
                
                if is_healthy:
                    scraper_info.consecutive_failures = 0
                else:
                    scraper_info.consecutive_failures += 1
                    
                    # Auto-disable if too many failures
                    if scraper_info.consecutive_failures >= scraper_info.max_failures:
                        self._logger.error(
                            "Disabling scraper due to consecutive failures",
                            name=name,
                            failures=scraper_info.consecutive_failures
                        )
                        scraper_info.is_enabled = False
                        await self.stop_scraper(name)
                
            except Exception as e:
                self._logger.error("Health check failed", scraper=name, error=str(e))
                results[name] = False
                self._scrapers[name].consecutive_failures += 1
        
        return results
    
    async def start_health_monitoring(self) -> None:
        """Start background health monitoring."""
        if self._is_monitoring:
            return
        
        self._is_monitoring = True
        self._health_check_task = asyncio.create_task(self._health_monitor_loop())
        self._logger.info("Health monitoring started")
    
    async def stop_health_monitoring(self) -> None:
        """Stop background health monitoring."""
        self._is_monitoring = False
        
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
            self._health_check_task = None
        
        self._logger.info("Health monitoring stopped")
    
    async def _health_monitor_loop(self) -> None:
        """Background health monitoring loop."""
        while self._is_monitoring:
            try:
                await self.health_check_all()
                await asyncio.sleep(self._health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._logger.error("Error in health monitor loop", error=str(e))
                await asyncio.sleep(60)  # Wait before retrying
    
    async def get_registry_status(self) -> Dict[str, Any]:
        """Get overall registry status."""
        total_scrapers = len(self._scrapers)
        running_scrapers = len(self._running_scrapers)
        enabled_scrapers = sum(1 for s in self._scrapers.values() if s.is_enabled)
        
        return {
            "total_scrapers": total_scrapers,
            "enabled_scrapers": enabled_scrapers,
            "running_scrapers": running_scrapers,
            "is_monitoring": self._is_monitoring,
            "health_check_interval": self._health_check_interval,
            "scrapers": self.list_scrapers()
        }
    
    async def cleanup(self) -> None:
        """Cleanup registry and stop all scrapers."""
        await self.stop_health_monitoring()
        await self.stop_all_scrapers()
        self._logger.info("Registry cleanup completed")


# Global registry instance
scraper_registry = ScraperRegistry()