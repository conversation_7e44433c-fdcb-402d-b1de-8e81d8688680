"""
Market data models for storing cryptocurrency information.
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import String, Float, Integer, DateTime, Text, Index
from sqlalchemy.orm import Mapped, mapped_column

from ..core.database import Base
from .base import TimestampMixin


class MarketData(Base, TimestampMixin):
    """Market data for cryptocurrencies."""
    
    __tablename__ = "market_data"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    symbol: Mapped[str] = mapped_column(String(20), nullable=False)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    price: Mapped[float] = mapped_column(Float, nullable=False)
    market_cap: Mapped[Optional[float]] = mapped_column(Float)
    volume_24h: Mapped[Optional[float]] = mapped_column(Float)
    price_change_24h: Mapped[Optional[float]] = mapped_column(Float)
    price_change_percentage_24h: Mapped[Optional[float]] = mapped_column(Float)
    circulating_supply: Mapped[Optional[float]] = mapped_column(Float)
    total_supply: Mapped[Optional[float]] = mapped_column(Float)
    max_supply: Mapped[Optional[float]] = mapped_column(Float)
    rank: Mapped[Optional[int]] = mapped_column(Integer)
    source: Mapped[str] = mapped_column(String(50), nullable=False)
    data_timestamp: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    
    # Indexes for efficient querying
    __table_args__ = (
        Index("ix_market_data_symbol_timestamp", "symbol", "data_timestamp"),
        Index("ix_market_data_source_timestamp", "source", "data_timestamp"),
        Index("ix_market_data_rank", "rank"),
    )


class TokenMetrics(Base, TimestampMixin):
    """Advanced metrics and scores for tokens."""
    
    __tablename__ = "token_metrics"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    symbol: Mapped[str] = mapped_column(String(20), nullable=False)
    
    # Quantum scores
    quantum_score: Mapped[Optional[float]] = mapped_column(Float)
    alpha_score: Mapped[Optional[float]] = mapped_column(Float)
    risk_score: Mapped[Optional[float]] = mapped_column(Float)
    
    # Social metrics
    social_momentum: Mapped[Optional[float]] = mapped_column(Float)
    social_sentiment: Mapped[Optional[float]] = mapped_column(Float)
    
    # On-chain metrics
    onchain_activity: Mapped[Optional[float]] = mapped_column(Float)
    holder_growth: Mapped[Optional[float]] = mapped_column(Float)
    whale_activity: Mapped[Optional[float]] = mapped_column(Float)
    
    # Technical metrics
    technical_strength: Mapped[Optional[float]] = mapped_column(Float)
    volume_momentum: Mapped[Optional[float]] = mapped_column(Float)
    
    # Metadata
    calculation_timestamp: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    model_version: Mapped[str] = mapped_column(String(20), nullable=False)
    
    # Indexes
    __table_args__ = (
        Index("ix_token_metrics_symbol_timestamp", "symbol", "calculation_timestamp"),
        Index("ix_token_metrics_quantum_score", "quantum_score"),
        Index("ix_token_metrics_alpha_score", "alpha_score"),
    )