"""
Models for tracking scraper performance and data.
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import String, Float, Integer, DateTime, Text, Boolean, JSON
from sqlalchemy.orm import Mapped, mapped_column

from ..core.database import Base
from .base import TimestampMixin


class ScraperRun(Base, TimestampMixin):
    """Track individual scraper execution runs."""
    
    __tablename__ = "scraper_runs"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    scraper_name: Mapped[str] = mapped_column(String(50), nullable=False)
    status: Mapped[str] = mapped_column(String(20), nullable=False)  # success, error, timeout
    start_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    end_time: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    duration_seconds: Mapped[Optional[float]] = mapped_column(Float)
    records_processed: Mapped[int] = mapped_column(Integer, default=0)
    error_message: Mapped[Optional[str]] = mapped_column(Text)
    metadata: Mapped[Optional[dict]] = mapped_column(JSON)


class ScraperMetrics(Base, TimestampMixin):
    """Aggregate metrics for scraper performance."""
    
    __tablename__ = "scraper_metrics"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    scraper_name: Mapped[str] = mapped_column(String(50), nullable=False)
    
    # Performance metrics
    total_requests: Mapped[int] = mapped_column(Integer, default=0)
    successful_requests: Mapped[int] = mapped_column(Integer, default=0)
    failed_requests: Mapped[int] = mapped_column(Integer, default=0)
    avg_response_time: Mapped[Optional[float]] = mapped_column(Float)
    
    # Rate limiting
    rate_limit_hits: Mapped[int] = mapped_column(Integer, default=0)
    
    # Circuit breaker
    circuit_breaker_trips: Mapped[int] = mapped_column(Integer, default=0)
    
    # Health status
    is_healthy: Mapped[bool] = mapped_column(Boolean, default=True)
    last_successful_run: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    last_error: Mapped[Optional[str]] = mapped_column(Text)
    
    # Time period for these metrics
    period_start: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    period_end: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)