"""
Configuration management for Alpha Grid.
Handles environment variables, API keys, and application settings.
"""

import os
from typing import Optional, Dict, Any
from pathlib import Path
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from cryptography.fernet import Fernet
import structlog

logger = structlog.get_logger(__name__)


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Application Settings
    app_name: str = "Alpha Grid"
    app_version: str = "1.0.0"
    debug: bool = False
    log_level: str = Field(default="INFO", alias="LOG_LEVEL")

    # Database Configuration
    database_url: str = Field(default="sqlite+aiosqlite:///./data/alpha_grid.db", alias="DATABASE_URL")
    redis_url: str = Field(default="redis://localhost:6379/0", alias="REDIS_URL")
    
    # API Keys
    coingecko_api_key: Optional[str] = Field(default=None, alias="COINGECKO_API_KEY")
    etherscan_api_key: Optional[str] = Field(default=None, alias="ETHERSCAN_API_KEY")
    dune_api_key: Optional[str] = Field(default=None, alias="DUNE_API_KEY")
    messari_api_key: Optional[str] = Field(default=None, alias="MESSARI_API_KEY")
    coinbase_api_key_name: Optional[str] = Field(default=None, alias="COINBASE_API_KEY_NAME")
    coinbase_api_private_key: Optional[str] = Field(default=None, alias="COINBASE_API_PRIVATE_KEY")
    infura_api_key: Optional[str] = Field(default=None, alias="INFURA_API_KEY")
    infura_api_secret: Optional[str] = Field(default=None, alias="INFURA_API_SECRET")
    web3_provider_url: Optional[str] = Field(default=None, alias="WEB3_PROVIDER_URL")
    
    # Rate Limits (requests per minute)
    coingecko_rate_limit: int = Field(default=50, alias="COINGECKO_RATE_LIMIT")
    etherscan_rate_limit: int = Field(default=200, alias="ETHERSCAN_RATE_LIMIT")
    binance_rate_limit: int = Field(default=1200, alias="BINANCE_RATE_LIMIT")
    coinbase_rate_limit: int = Field(default=100, alias="COINBASE_RATE_LIMIT")
    arkham_rate_limit: int = Field(default=60, alias="ARKHAM_RATE_LIMIT")

    # Circuit Breaker Settings
    failure_threshold: int = Field(default=5, alias="FAILURE_THRESHOLD")
    recovery_timeout: int = Field(default=60, alias="RECOVERY_TIMEOUT")
    request_timeout: int = Field(default=30, alias="REQUEST_TIMEOUT")

    # Security
    secret_key: str = Field(default="your-secret-key-here", alias="SECRET_KEY")
    algorithm: str = Field(default="HS256", alias="ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, alias="ACCESS_TOKEN_EXPIRE_MINUTES")

    # Worker Settings
    max_workers: int = Field(default=8, alias="MAX_WORKERS")
    
    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()

    @field_validator("database_url")
    @classmethod
    def validate_database_url(cls, v):
        """Ensure database directory exists."""
        if v.startswith("sqlite"):
            # Extract path from SQLite URL
            db_path = v.split("///")[-1]
            db_dir = Path(db_path).parent
            db_dir.mkdir(parents=True, exist_ok=True)
        return v
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "populate_by_name": True
    }


class ConfigManager:
    """Manages configuration and encrypted API keys."""
    
    def __init__(self):
        self.settings = Settings()
        self._encryption_key = None
        self._encrypted_keys: Dict[str, bytes] = {}
        
        # Initialize encryption if secret key is available
        if self.settings.secret_key != "your-secret-key-here":
            self._init_encryption()
    
    def _init_encryption(self):
        """Initialize encryption for API keys."""
        try:
            # Use the secret key to derive encryption key
            key = Fernet.generate_key()
            self._encryption_key = Fernet(key)
            logger.info("Encryption initialized for API keys")
        except Exception as e:
            logger.warning("Failed to initialize encryption", error=str(e))
    
    def encrypt_key(self, key_name: str, key_value: str) -> bool:
        """Encrypt and store an API key."""
        if not self._encryption_key:
            logger.warning("Encryption not available", key_name=key_name)
            return False
        
        try:
            encrypted_key = self._encryption_key.encrypt(key_value.encode())
            self._encrypted_keys[key_name] = encrypted_key
            logger.info("API key encrypted", key_name=key_name)
            return True
        except Exception as e:
            logger.error("Failed to encrypt API key", key_name=key_name, error=str(e))
            return False
    
    def get_decrypted_key(self, key_name: str) -> Optional[str]:
        """Get decrypted API key."""
        # First try to get from settings (environment variables)
        env_key = getattr(self.settings, key_name, None)
        if env_key:
            return env_key
        
        # Then try encrypted storage
        if not self._encryption_key or key_name not in self._encrypted_keys:
            return None
        
        try:
            encrypted_key = self._encrypted_keys[key_name]
            decrypted_key = self._encryption_key.decrypt(encrypted_key).decode()
            return decrypted_key
        except Exception as e:
            logger.error("Failed to decrypt API key", key_name=key_name, error=str(e))
            return None
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate current configuration and return status."""
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "api_keys_status": {}
        }
        
        # Check required API keys
        required_keys = [
            "coingecko_api_key",
            "etherscan_api_key",
            "web3_provider_url"
        ]
        
        for key in required_keys:
            value = self.get_decrypted_key(key)
            if not value:
                validation_results["warnings"].append(f"Missing API key: {key}")
                validation_results["api_keys_status"][key] = "missing"
            else:
                validation_results["api_keys_status"][key] = "present"
        
        # Check database path
        try:
            db_path = self.settings.database_url
            if db_path.startswith("sqlite"):
                db_file = db_path.split("///")[-1]
                db_dir = Path(db_file).parent
                if not db_dir.exists():
                    validation_results["errors"].append(f"Database directory does not exist: {db_dir}")
                    validation_results["valid"] = False
        except Exception as e:
            validation_results["errors"].append(f"Database configuration error: {str(e)}")
            validation_results["valid"] = False
        
        # Check secret key
        if self.settings.secret_key == "your-secret-key-here":
            validation_results["warnings"].append("Using default secret key - change in production")
        
        return validation_results


# Global configuration instance
config_manager = ConfigManager()
settings = config_manager.settings