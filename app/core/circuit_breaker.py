"""
Circuit breaker implementation for API resilience.
Prevents cascading failures by temporarily disabling failing services.
"""

import asyncio
import time
from enum import Enum
from typing import Any, Callable, Dict, Optional, Type, Union
from dataclasses import dataclass
import structlog

logger = structlog.get_logger(__name__)


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, requests blocked
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker."""
    failure_threshold: int = 5
    recovery_timeout: int = 60
    timeout: int = 30
    expected_exceptions: tuple = (Exception,)


class CircuitBreakerOpenError(Exception):
    """Raised when circuit breaker is open."""
    pass


class CircuitBreaker:
    """
    Circuit breaker implementation for protecting against cascading failures.
    """
    
    def __init__(self, name: str, config: CircuitBreakerConfig):
        self.name = name
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.last_failure_time = 0
        self.success_count = 0
        self._lock = asyncio.Lock()
        
        self.logger = logger.bind(circuit_breaker=name)
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function through circuit breaker.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            CircuitBreakerOpenError: When circuit is open
        """
        async with self._lock:
            # Check if we should transition from OPEN to HALF_OPEN
            if (self.state == CircuitBreakerState.OPEN and 
                time.time() - self.last_failure_time >= self.config.recovery_timeout):
                self.state = CircuitBreakerState.HALF_OPEN
                self.success_count = 0
                self.logger.info("Circuit breaker transitioning to HALF_OPEN")
        
        # Block requests if circuit is open
        if self.state == CircuitBreakerState.OPEN:
            self.logger.warning("Circuit breaker is OPEN, blocking request")
            raise CircuitBreakerOpenError(f"Circuit breaker {self.name} is open")
        
        try:
            # Execute the function with timeout
            if asyncio.iscoroutinefunction(func):
                result = await asyncio.wait_for(
                    func(*args, **kwargs), 
                    timeout=self.config.timeout
                )
            else:
                result = await asyncio.wait_for(
                    asyncio.to_thread(func, *args, **kwargs),
                    timeout=self.config.timeout
                )
            
            # Success - handle state transitions
            await self._on_success()
            return result
            
        except self.config.expected_exceptions as e:
            await self._on_failure(e)
            raise
        except asyncio.TimeoutError as e:
            await self._on_failure(e)
            raise
    
    async def _on_success(self):
        """Handle successful function execution."""
        async with self._lock:
            if self.state == CircuitBreakerState.HALF_OPEN:
                self.success_count += 1
                # If we have enough successes, close the circuit
                if self.success_count >= 3:  # Configurable threshold
                    self.state = CircuitBreakerState.CLOSED
                    self.failure_count = 0
                    self.logger.info("Circuit breaker CLOSED after successful recovery")
            elif self.state == CircuitBreakerState.CLOSED:
                # Reset failure count on success
                self.failure_count = 0
    
    async def _on_failure(self, exception: Exception):
        """Handle failed function execution."""
        async with self._lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            self.logger.warning(
                "Circuit breaker recorded failure",
                failure_count=self.failure_count,
                exception=str(exception)
            )
            
            # Transition to OPEN if we've exceeded the threshold
            if (self.state == CircuitBreakerState.CLOSED and 
                self.failure_count >= self.config.failure_threshold):
                self.state = CircuitBreakerState.OPEN
                self.logger.error("Circuit breaker OPENED due to failures")
            elif self.state == CircuitBreakerState.HALF_OPEN:
                # Go back to OPEN on any failure during half-open
                self.state = CircuitBreakerState.OPEN
                self.logger.warning("Circuit breaker returned to OPEN from HALF_OPEN")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get circuit breaker statistics."""
        return {
            "name": self.name,
            "state": self.state.value,
            "failure_count": self.failure_count,
            "success_count": self.success_count,
            "last_failure_time": self.last_failure_time,
            "config": {
                "failure_threshold": self.config.failure_threshold,
                "recovery_timeout": self.config.recovery_timeout,
                "timeout": self.config.timeout
            }
        }


class CircuitBreakerRegistry:
    """Registry for managing multiple circuit breakers."""
    
    def __init__(self):
        self._breakers: Dict[str, CircuitBreaker] = {}
        self.logger = logger.bind(component="circuit_breaker_registry")
    
    def get_circuit_breaker(self, name: str, config: CircuitBreakerConfig) -> CircuitBreaker:
        """Get or create a circuit breaker."""
        if name not in self._breakers:
            self._breakers[name] = CircuitBreaker(name, config)
            self.logger.info("Created new circuit breaker", name=name)
        
        return self._breakers[name]
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all circuit breakers."""
        return {name: breaker.get_stats() for name, breaker in self._breakers.items()}
    
    def reset_circuit_breaker(self, name: str) -> bool:
        """Reset a circuit breaker to closed state."""
        if name in self._breakers:
            breaker = self._breakers[name]
            breaker.state = CircuitBreakerState.CLOSED
            breaker.failure_count = 0
            breaker.success_count = 0
            self.logger.info("Circuit breaker reset", name=name)
            return True
        return False


# Global registry instance
_registry = CircuitBreakerRegistry()


def get_circuit_breaker(name: str, config: CircuitBreakerConfig) -> CircuitBreaker:
    """Get a circuit breaker from the global registry."""
    return _registry.get_circuit_breaker(name, config)


def get_all_circuit_breaker_stats() -> Dict[str, Dict[str, Any]]:
    """Get statistics for all circuit breakers."""
    return _registry.get_all_stats()


def reset_circuit_breaker(name: str) -> bool:
    """Reset a circuit breaker."""
    return _registry.reset_circuit_breaker(name)