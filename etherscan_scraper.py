"""
Etherscan API scraper for Ethereum on-chain data and whale movements.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog

from .base_scraper import BaseScraper
from alpha_grid.core.config import config_manager


logger = structlog.get_logger(__name__)


class EtherscanScraper(BaseScraper):
    """Etherscan API scraper for Ethereum blockchain data."""
    
    def __init__(self):
        """Initialize Etherscan scraper."""
        api_key = config_manager.get_decrypted_key('etherscan_api_key')
        super().__init__(
            name="Etherscan",
            rate_limit=config_manager.settings.etherscan_rate_limit,
            api_key=api_key,
            base_url="https://api.etherscan.io/v2/api"
        )
        
        # Cache for reducing API calls
        self._onchain_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 300  # 5 minutes
        
        # Known whale addresses for monitoring
        self.whale_addresses = [
            "******************************************",  # Binance 8
            "******************************************",  # Binance 7
            "******************************************",  # Binance 14
            "******************************************",  # Binance 2
            "******************************************",  # Binance 15
            "******************************************",  # Binance 16
            "******************************************",  # Binance 17
            "******************************************",  # Binance 18
            "******************************************",  # Binance 19
            "******************************************",  # Binance 20
        ]
        
        self.logger = logger.bind(scraper="Etherscan")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for Etherscan API."""
        return {}  # API key is passed as query parameter
    
    async def health_check(self) -> bool:
        """Check if Etherscan API is healthy."""
        try:
            # Test with a simple balance query
            params = {
                "chainid": 1,
                "module": "account",
                "action": "balance",
                "address": "******************************************",  # Ethereum Foundation
                "tag": "latest",
                "apikey": self.api_key
            }
            
            response = await self._make_request(self.base_url, params=params)
            return response.get("status") == "1" and "result" in response
        except Exception as e:
            self.logger.error("Health check failed", error=str(e))
            return False
    
    async def get_whale_transactions(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get recent large transactions from known whale addresses.
        
        Args:
            limit: Number of transactions to return per whale
            
        Returns:
            List of whale transaction data
        """
        try:
            whale_transactions = []
            
            for whale_address in self.whale_addresses[:5]:  # Limit to 5 whales to avoid rate limits
                try:
                    params = {
                        "chainid": 1,
                        "module": "account",
                        "action": "txlist",
                        "address": whale_address,
                        "startblock": 0,
                        "endblock": ********,
                        "page": 1,
                        "offset": min(limit, 10),  # Limit per whale
                        "sort": "desc",
                        "apikey": self.api_key
                    }
                    
                    self.logger.debug("Fetching whale transactions", address=whale_address)
                    
                    response = await self._make_request(self.base_url, params=params)
                    
                    if response.get("status") == "1" and "result" in response:
                        transactions = response["result"]
                        
                        for tx in transactions:
                            # Filter for large transactions (>10 ETH)
                            value_eth = int(tx.get("value", 0)) / 1e18
                            if value_eth > 10:
                                formatted_tx = self._format_transaction(tx, whale_address)
                                formatted_tx["whale_address"] = whale_address
                                formatted_tx["value_eth"] = value_eth
                                whale_transactions.append(formatted_tx)
                    
                    # Rate limiting
                    await asyncio.sleep(0.25)
                    
                except Exception as e:
                    self.logger.warning("Failed to get transactions for whale", address=whale_address, error=str(e))
                    continue
            
            # Sort by value and timestamp
            whale_transactions.sort(key=lambda x: (x.get("value_eth", 0), x.get("timestamp", 0)), reverse=True)
            
            result = whale_transactions[:limit]
            self.logger.info("Whale transactions fetched", count=len(result))
            
            return result
            
        except Exception as e:
            self.logger.error("Failed to get whale transactions", error=str(e))
            raise
    
    async def get_large_transactions(self, min_value_eth: float = 100) -> List[Dict[str, Any]]:
        """
        Get recent large transactions across the network.
        
        Args:
            min_value_eth: Minimum transaction value in ETH
            
        Returns:
            List of large transaction data
        """
        try:
            # Get latest block number first
            latest_block = await self._get_latest_block_number()
            if not latest_block:
                return []
            
            # Check last 100 blocks for large transactions
            start_block = max(0, latest_block - 100)
            large_transactions = []
            
            # Sample a few recent blocks to find large transactions
            for block_offset in range(0, 20, 5):  # Check every 5th block in last 20
                block_number = latest_block - block_offset
                
                try:
                    block_txs = await self._get_block_transactions(block_number)
                    
                    for tx in block_txs:
                        value_eth = int(tx.get("value", 0)) / 1e18
                        if value_eth >= min_value_eth:
                            formatted_tx = self._format_transaction(tx)
                            formatted_tx["value_eth"] = value_eth
                            formatted_tx["block_number"] = block_number
                            large_transactions.append(formatted_tx)
                    
                    await asyncio.sleep(0.2)  # Rate limiting
                    
                except Exception as e:
                    self.logger.warning("Failed to get block transactions", block=block_number, error=str(e))
                    continue
            
            # Sort by value
            large_transactions.sort(key=lambda x: x.get("value_eth", 0), reverse=True)
            
            result = large_transactions[:20]  # Top 20
            self.logger.info("Large transactions fetched", count=len(result), min_value=min_value_eth)
            
            return result
            
        except Exception as e:
            self.logger.error("Failed to get large transactions", error=str(e))
            raise
    
    async def get_gas_tracker(self) -> Dict[str, Any]:
        """
        Get current gas price information.
        
        Returns:
            Gas tracker data
        """
        try:
            params = {
                "chainid": 1,
                "module": "gastracker",
                "action": "gasoracle",
                "apikey": self.api_key
            }
            
            self.logger.debug("Fetching gas tracker data")
            
            response = await self._make_request(self.base_url, params=params)
            
            if response.get("status") == "1" and "result" in response:
                gas_data = response["result"]
                
                formatted_data = {
                    "safe_gas_price": float(gas_data.get("SafeGasPrice", 0)),
                    "standard_gas_price": float(gas_data.get("StandardGasPrice", 0)),
                    "fast_gas_price": float(gas_data.get("FastGasPrice", 0)),
                    "suggest_base_fee": float(gas_data.get("suggestBaseFee", 0)),
                    "gas_used_ratio": gas_data.get("gasUsedRatio", ""),
                    "timestamp": datetime.now().isoformat()
                }
                
                self.logger.info("Gas tracker data fetched")
                return formatted_data
            else:
                raise ValueError("Invalid response from gas tracker API")
                
        except Exception as e:
            self.logger.error("Failed to get gas tracker data", error=str(e))
            raise
    
    async def get_eth_supply(self) -> Dict[str, Any]:
        """
        Get Ethereum supply statistics.
        
        Returns:
            ETH supply data
        """
        try:
            params = {
                "chainid": 1,
                "module": "stats",
                "action": "ethsupply",
                "apikey": self.api_key
            }
            
            self.logger.debug("Fetching ETH supply data")
            
            response = await self._make_request(self.base_url, params=params)
            
            if response.get("status") == "1" and "result" in response:
                supply_wei = int(response["result"])
                supply_eth = supply_wei / 1e18
                
                supply_data = {
                    "total_supply_wei": supply_wei,
                    "total_supply_eth": supply_eth,
                    "timestamp": datetime.now().isoformat()
                }
                
                self.logger.info("ETH supply data fetched", supply_eth=f"{supply_eth:,.0f}")
                return supply_data
            else:
                raise ValueError("Invalid response from ETH supply API")
                
        except Exception as e:
            self.logger.error("Failed to get ETH supply data", error=str(e))
            raise
    
    async def get_onchain_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive on-chain summary.
        
        Returns:
            Complete on-chain analysis
        """
        try:
            # Get all on-chain data
            whale_txs = await self.get_whale_transactions(limit=10)
            large_txs = await self.get_large_transactions(min_value_eth=50)
            gas_data = await self.get_gas_tracker()
            supply_data = await self.get_eth_supply()
            
            # Calculate metrics
            total_whale_volume = sum(tx.get("value_eth", 0) for tx in whale_txs)
            total_large_volume = sum(tx.get("value_eth", 0) for tx in large_txs)
            avg_gas_price = (gas_data["safe_gas_price"] + gas_data["standard_gas_price"] + gas_data["fast_gas_price"]) / 3
            
            summary = {
                "whale_activity": {
                    "transaction_count": len(whale_txs),
                    "total_volume_eth": total_whale_volume,
                    "top_transactions": whale_txs[:5]
                },
                "large_transactions": {
                    "transaction_count": len(large_txs),
                    "total_volume_eth": total_large_volume,
                    "top_transactions": large_txs[:5]
                },
                "gas_metrics": gas_data,
                "supply_metrics": supply_data,
                "network_activity": self._assess_network_activity(whale_txs, large_txs, gas_data),
                "last_updated": datetime.now().isoformat()
            }
            
            self.logger.info("On-chain summary generated")
            return summary
            
        except Exception as e:
            self.logger.error("Failed to generate on-chain summary", error=str(e))
            raise
    
    async def _get_latest_block_number(self) -> Optional[int]:
        """Get the latest block number."""
        try:
            params = {
                "chainid": 1,
                "module": "proxy",
                "action": "eth_blockNumber",
                "apikey": self.api_key
            }
            
            response = await self._make_request(self.base_url, params=params)
            
            if "result" in response:
                return int(response["result"], 16)  # Convert hex to int
            return None
            
        except Exception as e:
            self.logger.warning("Failed to get latest block number", error=str(e))
            return None
    
    async def _get_block_transactions(self, block_number: int) -> List[Dict[str, Any]]:
        """Get transactions for a specific block."""
        try:
            params = {
                "chainid": 1,
                "module": "proxy",
                "action": "eth_getBlockByNumber",
                "tag": hex(block_number),
                "boolean": "true",
                "apikey": self.api_key
            }
            
            response = await self._make_request(self.base_url, params=params)
            
            if "result" in response and response["result"]:
                block_data = response["result"]
                return block_data.get("transactions", [])
            return []
            
        except Exception as e:
            self.logger.warning("Failed to get block transactions", block=block_number, error=str(e))
            return []
    
    def _format_transaction(self, tx: Dict[str, Any], whale_address: Optional[str] = None) -> Dict[str, Any]:
        """Format transaction data into standardized structure."""
        return {
            "hash": tx.get("hash"),
            "from": tx.get("from"),
            "to": tx.get("to"),
            "value": tx.get("value"),
            "gas": tx.get("gas"),
            "gas_price": tx.get("gasPrice"),
            "gas_used": tx.get("gasUsed"),
            "timestamp": int(tx.get("timeStamp", 0)),
            "readable_timestamp": self._format_timestamp(tx.get("timeStamp")),
            "block_number": int(tx.get("blockNumber", 0)),
            "transaction_index": int(tx.get("transactionIndex", 0)),
            "is_error": tx.get("isError") == "1",
            "input_data": tx.get("input", "")[:100] if tx.get("input") else "",  # Truncate input
        }
    
    def _format_timestamp(self, timestamp) -> str:
        """Format timestamp to readable string."""
        try:
            if timestamp:
                dt = datetime.fromtimestamp(int(timestamp))
                return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            pass
        return "Unknown"
    
    def _assess_network_activity(self, whale_txs: List, large_txs: List, gas_data: Dict) -> str:
        """Assess overall network activity level."""
        whale_count = len(whale_txs)
        large_count = len(large_txs)
        avg_gas = (gas_data["safe_gas_price"] + gas_data["standard_gas_price"] + gas_data["fast_gas_price"]) / 3
        
        if whale_count > 5 and large_count > 10 and avg_gas > 50:
            return "Very High - Significant whale activity and high gas prices"
        elif whale_count > 3 and large_count > 5 and avg_gas > 30:
            return "High - Notable large transactions and elevated gas"
        elif whale_count > 1 or large_count > 3:
            return "Moderate - Some large transaction activity"
        else:
            return "Low - Minimal large transaction activity"
