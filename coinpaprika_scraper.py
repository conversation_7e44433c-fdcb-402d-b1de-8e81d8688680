"""
CoinPaprika API scraper for backup market data and comprehensive crypto information.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog

from alpha_grid.agents.scrapers.base_scraper import BaseScraper
from alpha_grid.core.config import config_manager


logger = structlog.get_logger(__name__)


class CoinPaprikaScraper(BaseScraper):
    """CoinPaprika API scraper for comprehensive cryptocurrency market data."""
    
    def __init__(self):
        """Initialize CoinPaprika scraper."""
        super().__init__(
            name="CoinPaprika",
            rate_limit=config_manager.settings.coinpaprika_rate_limit,
            api_key=None,  # No API key required for free tier
            base_url="https://api.coinpaprika.com/v1"
        )
        
        # Cache for reducing API calls
        self._market_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 300  # 5 minutes
        
        self.logger = logger.bind(scraper="CoinPaprika")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers (none required for free tier)."""
        return {}
    
    async def health_check(self) -> bool:
        """Check if CoinPaprika API is healthy."""
        try:
            # Test with a simple ping
            url = f"{self.base_url}/ping"
            response = await self._make_request(url)
            # CoinPaprika ping returns {"status": "OK"}
            return isinstance(response, dict) and response.get("status") == "OK"
        except Exception as e:
            self.logger.error("Health check failed", error=str(e))
            return False
    
    async def get_market_data(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get comprehensive market data for top cryptocurrencies.
        
        Args:
            limit: Number of cryptocurrencies to return
            
        Returns:
            List of market data
        """
        try:
            # Check cache first
            cache_key = f"market_data_{limit}"
            if self._is_cache_valid() and cache_key in self._market_cache:
                self.logger.debug("Returning cached market data")
                return self._market_cache[cache_key]
            
            url = f"{self.base_url}/tickers"
            params = {"limit": limit}
            
            self.logger.debug("Fetching market data")
            
            response = await self._make_request(url, params=params)
            
            if not isinstance(response, list):
                raise ValueError("Invalid response format from CoinPaprika tickers API")
            
            market_data = []
            for coin in response:
                try:
                    usd_quotes = coin.get("quotes", {}).get("USD", {})
                    
                    formatted_coin = {
                        "id": coin.get("id"),
                        "name": coin.get("name"),
                        "symbol": coin.get("symbol"),
                        "rank": coin.get("rank"),
                        "price": usd_quotes.get("price", 0),
                        "volume_24h": usd_quotes.get("volume_24h", 0),
                        "volume_24h_change": usd_quotes.get("volume_24h_change_24h", 0),
                        "market_cap": usd_quotes.get("market_cap", 0),
                        "market_cap_change_24h": usd_quotes.get("market_cap_change_24h", 0),
                        "percent_change_1h": usd_quotes.get("percent_change_1h", 0),
                        "percent_change_24h": usd_quotes.get("percent_change_24h", 0),
                        "percent_change_7d": usd_quotes.get("percent_change_7d", 0),
                        "percent_change_30d": usd_quotes.get("percent_change_30d", 0),
                        "percent_change_1y": usd_quotes.get("percent_change_1y", 0),
                        "ath_price": usd_quotes.get("ath_price", 0),
                        "ath_date": usd_quotes.get("ath_date"),
                        "percent_from_ath": usd_quotes.get("percent_from_price_ath", 0),
                        "total_supply": coin.get("total_supply", 0),
                        "max_supply": coin.get("max_supply", 0),
                        "beta_value": coin.get("beta_value", 0),
                        "first_data_at": coin.get("first_data_at"),
                        "last_updated": coin.get("last_updated"),
                        "readable_last_updated": self._format_timestamp(coin.get("last_updated"))
                    }
                    
                    market_data.append(formatted_coin)
                    
                except (ValueError, TypeError) as e:
                    self.logger.warning("Failed to process coin data", coin_id=coin.get("id"), error=str(e))
                    continue
            
            # Cache the result
            self._market_cache[cache_key] = market_data
            self._cache_timestamp = datetime.now()
            
            self.logger.info("Market data fetched", count=len(market_data))
            return market_data
            
        except Exception as e:
            self.logger.error("Failed to get market data", error=str(e))
            raise
    
    async def get_coin_details(self, coin_id: str) -> Dict[str, Any]:
        """
        Get detailed information about a specific coin.
        
        Args:
            coin_id: CoinPaprika coin ID (e.g., 'btc-bitcoin')
            
        Returns:
            Detailed coin information
        """
        try:
            url = f"{self.base_url}/coins/{coin_id}"
            
            self.logger.debug("Fetching coin details", coin_id=coin_id)
            
            response = await self._make_request(url)
            
            if not isinstance(response, dict):
                raise ValueError(f"Invalid response format for coin {coin_id}")
            
            # Get current ticker data
            ticker_url = f"{self.base_url}/tickers/{coin_id}"
            ticker_response = await self._make_request(ticker_url)
            
            usd_quotes = ticker_response.get("quotes", {}).get("USD", {}) if ticker_response else {}
            
            coin_details = {
                "id": response.get("id"),
                "name": response.get("name"),
                "symbol": response.get("symbol"),
                "description": response.get("description", "")[:500],  # Truncate
                "type": response.get("type"),
                "tags": [tag.get("name") for tag in response.get("tags", [])],
                "team": [member.get("name") for member in response.get("team", [])],
                "started_at": response.get("started_at"),
                "development_status": response.get("development_status"),
                "hardware_wallet": response.get("hardware_wallet"),
                "proof_type": response.get("proof_type"),
                "org_structure": response.get("org_structure"),
                "hash_algorithm": response.get("hash_algorithm"),
                "links": response.get("links", {}),
                "whitepaper": response.get("whitepaper", {}),
                "current_price": usd_quotes.get("price", 0),
                "market_cap": usd_quotes.get("market_cap", 0),
                "volume_24h": usd_quotes.get("volume_24h", 0),
                "percent_change_24h": usd_quotes.get("percent_change_24h", 0),
                "last_updated": datetime.now().isoformat()
            }
            
            self.logger.info("Coin details fetched", coin_id=coin_id)
            return coin_details
            
        except Exception as e:
            self.logger.error("Failed to get coin details", coin_id=coin_id, error=str(e))
            raise
    
    async def get_global_stats(self) -> Dict[str, Any]:
        """
        Get global cryptocurrency market statistics.
        
        Returns:
            Global market statistics
        """
        try:
            url = f"{self.base_url}/global"
            
            self.logger.debug("Fetching global stats")
            
            response = await self._make_request(url)
            
            if not isinstance(response, dict):
                raise ValueError("Invalid response format from global stats API")
            
            global_stats = {
                "market_cap_usd": response.get("market_cap_usd", 0),
                "volume_24h_usd": response.get("volume_24h_usd", 0),
                "bitcoin_dominance_percentage": response.get("bitcoin_dominance_percentage", 0),
                "cryptocurrencies_number": response.get("cryptocurrencies_number", 0),
                "market_cap_ath_value": response.get("market_cap_ath_value", 0),
                "market_cap_ath_date": response.get("market_cap_ath_date"),
                "volume_24h_ath_value": response.get("volume_24h_ath_value", 0),
                "volume_24h_ath_date": response.get("volume_24h_ath_date"),
                "market_cap_change_24h": response.get("market_cap_change_24h", 0),
                "volume_24h_change_24h": response.get("volume_24h_change_24h", 0),
                "last_updated": response.get("last_updated"),
                "readable_last_updated": self._format_timestamp(response.get("last_updated"))
            }
            
            self.logger.info("Global stats fetched")
            return global_stats
            
        except Exception as e:
            self.logger.error("Failed to get global stats", error=str(e))
            raise
    
    async def get_trending_coins(self, timeframe: str = "24h") -> List[Dict[str, Any]]:
        """
        Get trending coins based on price changes.
        
        Args:
            timeframe: Time period for trending analysis
            
        Returns:
            List of trending coins
        """
        try:
            # Get market data and sort by performance
            market_data = await self.get_market_data(limit=100)
            
            # Sort by different criteria based on timeframe
            if timeframe == "1h":
                sorted_coins = sorted(market_data, key=lambda x: x.get("percent_change_1h", 0), reverse=True)
            elif timeframe == "7d":
                sorted_coins = sorted(market_data, key=lambda x: x.get("percent_change_7d", 0), reverse=True)
            elif timeframe == "30d":
                sorted_coins = sorted(market_data, key=lambda x: x.get("percent_change_30d", 0), reverse=True)
            else:  # Default to 24h
                sorted_coins = sorted(market_data, key=lambda x: x.get("percent_change_24h", 0), reverse=True)
            
            # Get top gainers and filter out stablecoins
            trending = []
            for coin in sorted_coins:
                # Skip stablecoins and very small market caps
                if (coin.get("symbol") in ["USDT", "USDC", "BUSD", "DAI", "TUSD"] or 
                    coin.get("market_cap", 0) < 10_000_000):
                    continue
                
                trending.append({
                    "id": coin.get("id"),
                    "name": coin.get("name"),
                    "symbol": coin.get("symbol"),
                    "rank": coin.get("rank"),
                    "price": coin.get("price"),
                    "percent_change": coin.get(f"percent_change_{timeframe}", coin.get("percent_change_24h", 0)),
                    "market_cap": coin.get("market_cap"),
                    "volume_24h": coin.get("volume_24h"),
                    "timeframe": timeframe
                })
                
                if len(trending) >= 20:  # Top 20 trending
                    break
            
            self.logger.info("Trending coins fetched", timeframe=timeframe, count=len(trending))
            return trending
            
        except Exception as e:
            self.logger.error("Failed to get trending coins", timeframe=timeframe, error=str(e))
            raise
    
    async def get_market_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive market summary.
        
        Returns:
            Complete market analysis
        """
        try:
            # Get all market data
            market_data = await self.get_market_data(limit=50)
            global_stats = await self.get_global_stats()
            trending_24h = await self.get_trending_coins("24h")
            trending_7d = await self.get_trending_coins("7d")
            
            # Calculate market insights
            total_market_cap = global_stats.get("market_cap_usd", 0)
            btc_dominance = global_stats.get("bitcoin_dominance_percentage", 0)
            total_volume = global_stats.get("volume_24h_usd", 0)
            
            # Analyze market sentiment
            positive_24h = len([c for c in market_data if c.get("percent_change_24h", 0) > 0])
            negative_24h = len(market_data) - positive_24h
            
            summary = {
                "global_metrics": global_stats,
                "market_overview": {
                    "total_market_cap": total_market_cap,
                    "total_volume_24h": total_volume,
                    "bitcoin_dominance": btc_dominance,
                    "cryptocurrencies_tracked": global_stats.get("cryptocurrencies_number", 0),
                    "positive_24h_count": positive_24h,
                    "negative_24h_count": negative_24h,
                    "market_sentiment": self._assess_market_sentiment(positive_24h, len(market_data), btc_dominance)
                },
                "top_coins": market_data[:10],
                "trending_24h": trending_24h[:10],
                "trending_7d": trending_7d[:10],
                "market_insights": self._generate_market_insights(market_data, global_stats),
                "last_updated": datetime.now().isoformat()
            }
            
            self.logger.info("Market summary generated")
            return summary
            
        except Exception as e:
            self.logger.error("Failed to generate market summary", error=str(e))
            raise
    
    def _format_timestamp(self, timestamp_str) -> str:
        """Format timestamp string to readable format."""
        try:
            if timestamp_str:
                # Handle both string and integer timestamps
                if isinstance(timestamp_str, int):
                    dt = datetime.fromtimestamp(timestamp_str)
                else:
                    # Handle ISO format string
                    dt = datetime.fromisoformat(str(timestamp_str).replace('Z', '+00:00'))
                return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            pass
        return "Unknown"
    
    def _assess_market_sentiment(self, positive_count: int, total_count: int, btc_dominance: float) -> str:
        """Assess overall market sentiment."""
        positive_ratio = positive_count / total_count if total_count > 0 else 0
        
        if positive_ratio > 0.7 and btc_dominance < 50:
            return "Very Bullish - Strong altcoin season"
        elif positive_ratio > 0.6:
            return "Bullish - Most coins gaining"
        elif positive_ratio > 0.4:
            return "Mixed - Balanced market"
        elif positive_ratio > 0.3:
            return "Bearish - Most coins declining"
        else:
            return "Very Bearish - Widespread selling"
    
    def _generate_market_insights(self, market_data: List, global_stats: Dict) -> str:
        """Generate market insights based on data."""
        insights = []
        
        # Market cap insights
        total_cap = global_stats.get("market_cap_usd", 0)
        if total_cap > 3_000_000_000_000:  # $3T
            insights.append("Market cap above $3T")
        elif total_cap > 2_000_000_000_000:  # $2T
            insights.append("Market cap above $2T")
        
        # Bitcoin dominance insights
        btc_dom = global_stats.get("bitcoin_dominance_percentage", 0)
        if btc_dom > 60:
            insights.append("High BTC dominance")
        elif btc_dom < 40:
            insights.append("Low BTC dominance - altcoin season")
        
        # Volume insights
        volume_change = global_stats.get("volume_24h_change_24h", 0)
        if volume_change > 20:
            insights.append("Volume surge (+20%)")
        elif volume_change < -20:
            insights.append("Volume decline (-20%)")
        
        return " | ".join(insights) if insights else "Standard market conditions"
    
    def _is_cache_valid(self) -> bool:
        """Check if cache is still valid."""
        if self._cache_timestamp is None:
            return False
        
        return (datetime.now() - self._cache_timestamp).total_seconds() < self._cache_ttl
