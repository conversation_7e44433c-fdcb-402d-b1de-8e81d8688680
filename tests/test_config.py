"""
Test configuration and core infrastructure.
"""

import pytest
import asyncio
from pathlib import Path
import sys
import os

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.config import settings, config_manager
from app.core.logging import setup_logging, get_logger
from app.core.circuit_breaker import <PERSON><PERSON>reak<PERSON>, CircuitBreakerConfig, CircuitBreakerState
from app.core.database import db_manager, init_database


class TestConfiguration:
    """Test configuration management."""
    
    def test_settings_load(self):
        """Test that settings load correctly."""
        assert settings.app_name == "Alpha Grid"
        assert settings.app_version == "1.0.0"
        assert settings.log_level in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    
    def test_api_keys_present(self):
        """Test that API keys are loaded."""
        # These should be present from .env file
        assert settings.coingecko_api_key is not None
        assert settings.etherscan_api_key is not None
        assert settings.web3_provider_url is not None
    
    def test_rate_limits(self):
        """Test rate limit configurations."""
        assert settings.coingecko_rate_limit > 0
        assert settings.etherscan_rate_limit > 0
        assert settings.binance_rate_limit > 0
    
    def test_config_validation(self):
        """Test configuration validation."""
        validation = config_manager.validate_configuration()
        assert isinstance(validation, dict)
        assert "valid" in validation
        assert "errors" in validation
        assert "warnings" in validation
        assert "api_keys_status" in validation


class TestLogging:
    """Test logging configuration."""
    
    def test_setup_logging(self):
        """Test logging setup."""
        setup_logging()
        logger = get_logger("test")
        assert logger is not None
        
        # Test logging
        logger.info("Test log message", test_param="test_value")
    
    def test_logger_creation(self):
        """Test logger creation."""
        logger1 = get_logger("test1")
        logger2 = get_logger("test2")
        
        assert logger1 is not None
        assert logger2 is not None


class TestCircuitBreaker:
    """Test circuit breaker functionality."""
    
    def test_circuit_breaker_creation(self):
        """Test circuit breaker creation."""
        config = CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=30,
            timeout=10
        )
        
        cb = CircuitBreaker("test_service", config)
        assert cb.name == "test_service"
        assert cb.state == CircuitBreakerState.CLOSED
        assert cb.failure_count == 0
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_success(self):
        """Test successful function execution through circuit breaker."""
        config = CircuitBreakerConfig(failure_threshold=3, recovery_timeout=30, timeout=10)
        cb = CircuitBreaker("test_success", config)
        
        async def success_func():
            return "success"
        
        result = await cb.call(success_func)
        assert result == "success"
        assert cb.state == CircuitBreakerState.CLOSED
        assert cb.failure_count == 0
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_failure(self):
        """Test circuit breaker failure handling."""
        config = CircuitBreakerConfig(failure_threshold=2, recovery_timeout=30, timeout=10)
        cb = CircuitBreaker("test_failure", config)
        
        async def failing_func():
            raise ValueError("Test error")
        
        # First failure
        with pytest.raises(ValueError):
            await cb.call(failing_func)
        assert cb.failure_count == 1
        assert cb.state == CircuitBreakerState.CLOSED
        
        # Second failure - should open circuit
        with pytest.raises(ValueError):
            await cb.call(failing_func)
        assert cb.failure_count == 2
        assert cb.state == CircuitBreakerState.OPEN


@pytest.mark.asyncio
class TestDatabase:
    """Test database functionality."""
    
    async def test_database_initialization(self):
        """Test database initialization."""
        await db_manager.initialize()
        assert db_manager.engine is not None
        assert db_manager.session_factory is not None
    
    async def test_database_health_check(self):
        """Test database health check."""
        health = await db_manager.health_check()
        assert isinstance(health, bool)
    
    async def test_database_session(self):
        """Test database session creation."""
        await db_manager.initialize()
        
        async for session in db_manager.get_session():
            assert session is not None
            break  # Just test that we can get a session


if __name__ == "__main__":
    # Run basic tests
    import asyncio
    
    async def run_async_tests():
        """Run async tests manually."""
        print("Testing configuration...")
        config_test = TestConfiguration()
        config_test.test_settings_load()
        config_test.test_api_keys_present()
        config_test.test_rate_limits()
        config_test.test_config_validation()
        print("✅ Configuration tests passed")
        
        print("Testing logging...")
        logging_test = TestLogging()
        logging_test.test_setup_logging()
        logging_test.test_logger_creation()
        print("✅ Logging tests passed")
        
        print("Testing circuit breaker...")
        cb_test = TestCircuitBreaker()
        cb_test.test_circuit_breaker_creation()
        await cb_test.test_circuit_breaker_success()
        await cb_test.test_circuit_breaker_failure()
        print("✅ Circuit breaker tests passed")
        
        print("Testing database...")
        db_test = TestDatabase()
        await db_test.test_database_initialization()
        await db_test.test_database_health_check()
        await db_test.test_database_session()
        print("✅ Database tests passed")
        
        print("🎉 All Phase 1 tests passed!")
    
    asyncio.run(run_async_tests())