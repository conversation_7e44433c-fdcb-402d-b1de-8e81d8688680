"""
Test quantum computing modules.
"""

import pytest
import asyncio
import numpy as np
import polars as pl
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.agents.quantum.quantum_feature_encoder import QuantumFeatureEncoder
from app.agents.quantum.quantum_circuit_optimizer import QuantumCircuitOptimizer
from app.agents.quantum.quantum_score_calculator import QuantumScoreCalculator


class TestQuantumFeatureEncoder:
    """Test quantum feature encoder."""
    
    def test_encoder_creation(self):
        """Test encoder creation with different configurations."""
        # Test amplitude encoding
        encoder = QuantumFeatureEncoder(num_qubits=3, encoding_type="amplitude")
        assert encoder.num_qubits == 3
        assert encoder.encoding_type == "amplitude"
        assert encoder.max_features == 8  # 2^3
        
        # Test angle encoding
        encoder = QuantumFeatureEncoder(num_qubits=4, encoding_type="angle")
        assert encoder.num_qubits == 4
        assert encoder.encoding_type == "angle"
        assert encoder.max_features == 4  # num_qubits for angle encoding
    
    def test_feature_normalization(self):
        """Test feature normalization for different encoding types."""
        encoder = QuantumFeatureEncoder(num_qubits=3, encoding_type="amplitude")
        
        # Test with simple features
        features = np.array([[1, 2, 3, 4, 5, 6, 7, 8]])
        normalized = encoder.normalize_features(features)
        
        assert normalized.shape == (1, 8)
        # For amplitude encoding, should be normalized to unit vector
        assert np.isclose(np.linalg.norm(normalized[0]), 1.0, atol=1e-6)
    
    def test_feature_padding_and_truncation(self):
        """Test feature padding and truncation."""
        encoder = QuantumFeatureEncoder(num_qubits=2, encoding_type="amplitude")  # max 4 features
        
        # Test truncation
        features = np.array([[1, 2, 3, 4, 5, 6]])  # 6 features, should truncate to 4
        normalized = encoder.normalize_features(features)
        assert normalized.shape == (1, 4)
        
        # Test padding
        features = np.array([[1, 2]])  # 2 features, should pad to 4
        normalized = encoder.normalize_features(features)
        assert normalized.shape == (1, 4)
        assert normalized[0, 2] == 0  # Padded values should be 0
        assert normalized[0, 3] == 0
    
    def test_circuit_creation(self):
        """Test quantum circuit creation for different encoding types."""
        encoder = QuantumFeatureEncoder(num_qubits=2, encoding_type="amplitude")
        
        # Test amplitude encoding circuit
        amplitudes = np.array([0.5, 0.5, 0.5, 0.5])
        circuit = encoder.create_amplitude_encoding_circuit(amplitudes)
        assert circuit.num_qubits == 2
        assert circuit.num_clbits == 2  # Should have measurements
        
        # Test angle encoding circuit
        encoder_angle = QuantumFeatureEncoder(num_qubits=2, encoding_type="angle")
        angles = np.array([np.pi/4, np.pi/2])
        circuit = encoder_angle.create_angle_encoding_circuit(angles)
        assert circuit.num_qubits == 2
        assert circuit.num_clbits == 2
    
    def test_market_feature_extraction(self):
        """Test extraction of features from market data."""
        encoder = QuantumFeatureEncoder(num_qubits=3, encoding_type="amplitude")
        
        # Create sample market data
        market_data = pl.DataFrame({
            "symbol": ["BTC", "ETH", "ADA"],
            "price_change_percentage_24h": [5.2, -2.1, 8.7],
            "volume_to_market_cap_ratio": [0.05, 0.08, 0.12],
            "market_cap_rank": [1, 2, 5],
            "circulating_supply": [19000000, 120000000, 35000000000]
        })
        
        features = encoder.extract_market_features(market_data)
        assert features.shape[0] == 3  # 3 cryptocurrencies
        assert features.shape[1] >= 4  # At least 4 base features
        assert not np.isnan(features).any()  # No NaN values
    
    def test_encoding_stats(self):
        """Test encoding statistics."""
        encoder = QuantumFeatureEncoder(num_qubits=4, encoding_type="angle")
        stats = encoder.get_encoding_stats()
        
        assert "num_qubits" in stats
        assert "encoding_type" in stats
        assert "max_features" in stats
        assert "supported_encodings" in stats
        assert stats["num_qubits"] == 4
        assert stats["encoding_type"] == "angle"


class TestQuantumCircuitOptimizer:
    """Test quantum circuit optimizer."""
    
    def test_optimizer_creation(self):
        """Test optimizer creation."""
        optimizer = QuantumCircuitOptimizer()
        assert optimizer.target_backend == "fake_montreal"
        assert optimizer.backend is not None
    
    def test_circuit_optimization(self):
        """Test basic circuit optimization."""
        from qiskit import QuantumCircuit
        
        optimizer = QuantumCircuitOptimizer()
        
        # Create a simple test circuit
        qc = QuantumCircuit(2)
        qc.h(0)
        qc.cx(0, 1)
        qc.h(0)  # This H gate can potentially be optimized
        qc.measure_all()
        
        original_depth = qc.depth()
        original_size = qc.size()
        
        # Optimize circuit
        optimized_qc, metrics = optimizer.optimize_circuit(qc, optimization_level=1)
        
        assert "original_depth" in metrics
        assert "optimized_depth" in metrics
        assert "optimization_time" in metrics
        assert metrics["original_depth"] == original_depth
        assert metrics["original_size"] == original_size
        assert optimized_qc.num_qubits == qc.num_qubits
    
    def test_circuit_analysis(self):
        """Test circuit complexity analysis."""
        from qiskit import QuantumCircuit
        
        optimizer = QuantumCircuitOptimizer()
        
        # Create a test circuit
        qc = QuantumCircuit(3)
        qc.h(0)
        qc.cx(0, 1)
        qc.cx(1, 2)
        qc.measure_all()
        
        analysis = optimizer.analyze_circuit_complexity(qc)
        
        assert "depth" in analysis
        assert "size" in analysis
        assert "num_qubits" in analysis
        assert "complexity_score" in analysis
        assert "recommendations" in analysis
        assert analysis["num_qubits"] == 3
        assert 0 <= analysis["complexity_score"] <= 1
    
    def test_optimization_statistics(self):
        """Test optimization statistics tracking."""
        optimizer = QuantumCircuitOptimizer()
        
        # Get initial stats
        stats = optimizer.get_optimization_statistics()
        assert stats["circuits_optimized"] == 0
        
        # Reset stats
        optimizer.reset_statistics()
        stats = optimizer.get_optimization_statistics()
        assert stats["circuits_optimized"] == 0


class TestQuantumScoreCalculator:
    """Test quantum score calculator."""
    
    def test_calculator_creation(self):
        """Test calculator creation with different configurations."""
        calc = QuantumScoreCalculator(
            num_qubits=3,
            num_layers=1,
            encoding_type="amplitude",
            optimizer_type="SPSA"
        )
        
        assert calc.num_qubits == 3
        assert calc.num_layers == 1
        assert calc.encoding_type == "amplitude"
        assert calc.optimizer_type == "SPSA"
        assert not calc.is_trained
        assert calc.feature_encoder is not None
        assert calc.circuit_optimizer is not None
    
    def test_quantum_model_creation(self):
        """Test quantum model creation."""
        calc = QuantumScoreCalculator(num_qubits=2, num_layers=1)
        
        ansatz, observable = calc.create_quantum_model()
        
        assert ansatz.num_qubits == 2
        assert ansatz.num_parameters > 0
        assert observable is not None
    
    def test_training_data_preparation(self):
        """Test training data preparation."""
        calc = QuantumScoreCalculator(num_qubits=3, num_layers=1)
        
        # Create sample market data
        market_data = pl.DataFrame({
            "symbol": ["BTC", "ETH", "ADA", "DOT"],
            "price_change_percentage_24h": [5.2, -2.1, 8.7, -1.5],
            "volume_to_market_cap_ratio": [0.05, 0.08, 0.12, 0.06],
            "market_cap_rank": [1, 2, 5, 8],
            "circulating_supply": [19000000, 120000000, 35000000000, 1200000000]
        })
        
        features, targets = calc.prepare_training_data(market_data)
        
        assert features.shape[0] == 4  # 4 samples
        assert features.shape[1] > 0  # Some features
        assert targets.shape[0] == 4  # 4 targets
        assert set(targets) <= {0, 1}  # Binary targets
    
    @patch('qiskit.primitives.Estimator')
    def test_untrained_score_calculation(self, mock_estimator):
        """Test score calculation when model is not trained."""
        calc = QuantumScoreCalculator(num_qubits=2, num_layers=1)
        
        # Create sample data
        market_data = pl.DataFrame({
            "symbol": ["BTC", "ETH"],
            "price_change_percentage_24h": [5.2, -2.1],
            "volume_to_market_cap_ratio": [0.05, 0.08],
            "market_cap_rank": [1, 2],
            "circulating_supply": [19000000, 120000000]
        })
        
        # Should return random scores since not trained
        results = calc.calculate_quantum_score(market_data)
        
        assert "quantum_scores" in results
        assert "confidence_scores" in results
        assert "warning" in results
        assert len(results["quantum_scores"]) == 2
        assert not results["model_trained"]
    
    def test_model_info(self):
        """Test model information retrieval."""
        calc = QuantumScoreCalculator(num_qubits=4, num_layers=2)
        
        info = calc.get_model_info()
        
        assert "is_trained" in info
        assert "num_qubits" in info
        assert "num_layers" in info
        assert "encoding_type" in info
        assert "calculation_stats" in info
        assert info["num_qubits"] == 4
        assert info["num_layers"] == 2
        assert not info["is_trained"]
    
    def test_optimizer_selection(self):
        """Test optimizer selection."""
        calc = QuantumScoreCalculator(optimizer_type="SPSA")
        optimizer = calc._get_optimizer()
        assert optimizer.__class__.__name__ == "SPSA"
        
        calc = QuantumScoreCalculator(optimizer_type="COBYLA")
        optimizer = calc._get_optimizer()
        assert optimizer.__class__.__name__ == "COBYLA"
        
        # Test unknown optimizer defaults to SPSA
        calc = QuantumScoreCalculator(optimizer_type="UNKNOWN")
        optimizer = calc._get_optimizer()
        assert optimizer.__class__.__name__ == "SPSA"


if __name__ == "__main__":
    # Run basic tests
    def run_tests():
        """Run tests manually."""
        print("Testing Quantum Feature Encoder...")
        encoder_test = TestQuantumFeatureEncoder()
        encoder_test.test_encoder_creation()
        encoder_test.test_feature_normalization()
        encoder_test.test_feature_padding_and_truncation()
        encoder_test.test_circuit_creation()
        encoder_test.test_market_feature_extraction()
        encoder_test.test_encoding_stats()
        print("✅ Quantum Feature Encoder tests passed")
        
        print("Testing Quantum Circuit Optimizer...")
        optimizer_test = TestQuantumCircuitOptimizer()
        optimizer_test.test_optimizer_creation()
        optimizer_test.test_circuit_optimization()
        optimizer_test.test_circuit_analysis()
        optimizer_test.test_optimization_statistics()
        print("✅ Quantum Circuit Optimizer tests passed")
        
        print("Testing Quantum Score Calculator...")
        calc_test = TestQuantumScoreCalculator()
        calc_test.test_calculator_creation()
        calc_test.test_quantum_model_creation()
        calc_test.test_training_data_preparation()
        calc_test.test_untrained_score_calculation()
        calc_test.test_model_info()
        calc_test.test_optimizer_selection()
        print("✅ Quantum Score Calculator tests passed")
        
        print("🎉 All Phase 3 quantum tests passed!")
    
    run_tests()