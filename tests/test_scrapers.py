"""
Test scraper infrastructure and implementations.
"""

import pytest
import asyncio
import time
from unittest.mock import AsyncMock, patch
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.agents.scrapers.base_scraper import <PERSON><PERSON><PERSON>rap<PERSON>, TokenBucket
from app.agents.scrapers.registry import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ScraperInfo
from app.agents.scrapers.coingecko_scraper import CoinGeckoScraper
from app.agents.scrapers.etherscan_scraper import EtherscanScraper
from app.core.config import settings


class MockScraper(BaseScraper):
    """Mock scraper for testing."""
    
    def __init__(self, name="<PERSON>ckScraper", rate_limit=60):
        super().__init__(name=name, rate_limit=rate_limit)
        self.health_status = True
    
    def _get_auth_headers(self):
        return {"Authorization": "Bearer test-token"}
    
    async def health_check(self):
        return self.health_status


class TestTokenBucket:
    """Test token bucket rate limiter."""
    
    @pytest.mark.asyncio
    async def test_token_bucket_creation(self):
        """Test token bucket creation."""
        bucket = TokenBucket(rate=60, capacity=100)
        assert bucket.rate == 1.0  # 60 per minute = 1 per second
        assert bucket.capacity == 100
        assert bucket.tokens == 100
    
    @pytest.mark.asyncio
    async def test_token_acquisition(self):
        """Test token acquisition."""
        bucket = TokenBucket(rate=60, capacity=10)

        # Should be able to acquire tokens initially
        assert await bucket.acquire(5) == True
        assert 4.5 <= bucket.tokens <= 5.5  # Allow for small timing differences

        # Should not be able to acquire more than available
        assert await bucket.acquire(10) == False
        assert 4.5 <= bucket.tokens <= 5.5  # Allow for small refill
    
    @pytest.mark.asyncio
    async def test_token_refill(self):
        """Test token refill over time."""
        bucket = TokenBucket(rate=60, capacity=10)  # 1 token per second
        
        # Drain all tokens
        await bucket.acquire(10)
        assert bucket.tokens == 0
        
        # Wait for refill
        await asyncio.sleep(2.1)  # Wait for 2+ tokens to refill
        
        # Should have refilled some tokens
        assert await bucket.acquire(2) == True


class TestBaseScraper:
    """Test base scraper functionality."""
    
    @pytest.mark.asyncio
    async def test_scraper_creation(self):
        """Test scraper creation."""
        scraper = MockScraper()
        assert scraper.name == "MockScraper"
        assert scraper.rate_limit == 60
        assert scraper.token_bucket is not None
        assert scraper.client is not None
        await scraper.close()
    
    @pytest.mark.asyncio
    async def test_health_check(self):
        """Test health check."""
        scraper = MockScraper()
        assert await scraper.health_check() == True
        
        scraper.health_status = False
        assert await scraper.health_check() == False
        await scraper.close()
    
    @pytest.mark.asyncio
    async def test_metrics_collection(self):
        """Test metrics collection."""
        scraper = MockScraper()
        
        # Initial metrics
        metrics = await scraper.get_metrics()
        assert metrics["name"] == "MockScraper"
        assert metrics["request_count"] == 0
        assert metrics["success_count"] == 0
        assert metrics["error_count"] == 0
        
        await scraper.close()
    
    @pytest.mark.asyncio
    async def test_caching(self):
        """Test data caching."""
        scraper = MockScraper()
        
        # Test cache miss
        cache_key = scraper._get_cache_key("test", "data")
        cached_data = scraper._get_cached_data(cache_key)
        assert cached_data is None
        
        # Test cache set and hit
        test_data = {"test": "value"}
        scraper._cache_data(cache_key, test_data)
        cached_data = scraper._get_cached_data(cache_key)
        assert cached_data == test_data
        
        await scraper.close()
    
    @pytest.mark.asyncio
    async def test_cache_expiry(self):
        """Test cache expiry."""
        scraper = MockScraper()
        scraper._cache_ttl = 1  # 1 second TTL
        
        # Cache some data
        cache_key = scraper._get_cache_key("test", "expiry")
        test_data = {"test": "expiry"}
        scraper._cache_data(cache_key, test_data)
        
        # Should be cached initially
        assert scraper._get_cached_data(cache_key) == test_data
        
        # Wait for expiry
        await asyncio.sleep(1.1)
        
        # Should be expired now
        assert scraper._get_cached_data(cache_key) is None
        
        await scraper.close()


class TestScraperRegistry:
    """Test scraper registry functionality."""
    
    @pytest.mark.asyncio
    async def test_registry_creation(self):
        """Test registry creation."""
        registry = ScraperRegistry()
        assert len(registry._scrapers) == 0
        assert len(registry._running_scrapers) == 0
    
    @pytest.mark.asyncio
    async def test_scraper_registration(self):
        """Test scraper registration."""
        registry = ScraperRegistry()
        
        registry.register_scraper(
            name="test_scraper",
            scraper_class=MockScraper,
            priority=2,
            tags={"test", "mock"}
        )
        
        assert "test_scraper" in registry._scrapers
        scraper_info = registry._scrapers["test_scraper"]
        assert scraper_info.name == "test_scraper"
        assert scraper_info.scraper_class == MockScraper
        assert scraper_info.priority == 2
        assert "test" in scraper_info.tags
    
    @pytest.mark.asyncio
    async def test_scraper_start_stop(self):
        """Test starting and stopping scrapers."""
        registry = ScraperRegistry()
        
        registry.register_scraper("test_scraper", MockScraper)
        
        # Start scraper
        result = await registry.start_scraper("test_scraper")
        assert result == True
        assert "test_scraper" in registry._running_scrapers
        
        # Stop scraper
        result = await registry.stop_scraper("test_scraper")
        assert result == True
        assert "test_scraper" not in registry._running_scrapers
    
    @pytest.mark.asyncio
    async def test_health_monitoring(self):
        """Test health monitoring."""
        registry = ScraperRegistry()
        
        registry.register_scraper("test_scraper", MockScraper)
        await registry.start_scraper("test_scraper")
        
        # Test health check
        health_results = await registry.health_check_all()
        assert health_results["test_scraper"] == True
        
        # Test with failing health check
        scraper = registry.get_scraper("test_scraper")
        if hasattr(scraper, 'health_status'):
            scraper.health_status = False

            health_results = await registry.health_check_all()
            assert health_results["test_scraper"] == False
        
        await registry.cleanup()


@pytest.mark.asyncio
class TestCoinGeckoScraper:
    """Test CoinGecko scraper implementation."""
    
    async def test_scraper_creation(self):
        """Test CoinGecko scraper creation."""
        scraper = CoinGeckoScraper()
        assert scraper.name == "CoinGecko"
        assert scraper.base_url == "https://api.coingecko.com/api/v3"
        await scraper.close()
    
    async def test_auth_headers(self):
        """Test authentication headers."""
        scraper = CoinGeckoScraper()
        headers = scraper._get_auth_headers()
        
        if settings.coingecko_api_key:
            assert "x-cg-demo-api-key" in headers
            assert headers["x-cg-demo-api-key"] == settings.coingecko_api_key
        else:
            assert headers == {}
        
        await scraper.close()
    
    @patch('httpx.AsyncClient.request')
    async def test_health_check_success(self, mock_request):
        """Test successful health check."""
        # Mock successful ping response
        mock_response = AsyncMock()
        mock_response.json.return_value = {"gecko_says": "(V3) To the Moon!"}
        mock_response.raise_for_status.return_value = None
        mock_request.return_value = mock_response
        
        scraper = CoinGeckoScraper()
        result = await scraper.health_check()
        assert result == True
        await scraper.close()
    
    @patch('httpx.AsyncClient.request')
    async def test_health_check_failure(self, mock_request):
        """Test failed health check."""
        # Mock failed response
        mock_request.side_effect = Exception("Connection failed")
        
        scraper = CoinGeckoScraper()
        result = await scraper.health_check()
        assert result == False
        await scraper.close()


@pytest.mark.asyncio
class TestEtherscanScraper:
    """Test Etherscan scraper implementation."""
    
    async def test_scraper_creation(self):
        """Test Etherscan scraper creation."""
        scraper = EtherscanScraper()
        assert scraper.name == "Etherscan"
        assert scraper.base_url == "https://api.etherscan.io/api"
        await scraper.close()
    
    async def test_auth_headers(self):
        """Test authentication headers (should be empty for Etherscan)."""
        scraper = EtherscanScraper()
        headers = scraper._get_auth_headers()
        assert headers == {}
        await scraper.close()
    
    async def test_base_params(self):
        """Test base parameters include API key."""
        scraper = EtherscanScraper()
        params = scraper._get_base_params()
        
        if settings.etherscan_api_key:
            assert "apikey" in params
            assert params["apikey"] == settings.etherscan_api_key
        else:
            assert params == {}
        
        await scraper.close()


if __name__ == "__main__":
    # Run basic tests
    async def run_async_tests():
        """Run async tests manually."""
        print("Testing Token Bucket...")
        bucket_test = TestTokenBucket()
        await bucket_test.test_token_bucket_creation()
        await bucket_test.test_token_acquisition()
        print("✅ Token Bucket tests passed")
        
        print("Testing Base Scraper...")
        scraper_test = TestBaseScraper()
        await scraper_test.test_scraper_creation()
        await scraper_test.test_health_check()
        await scraper_test.test_metrics_collection()
        await scraper_test.test_caching()
        print("✅ Base Scraper tests passed")
        
        print("Testing Scraper Registry...")
        registry_test = TestScraperRegistry()
        await registry_test.test_registry_creation()
        await registry_test.test_scraper_registration()
        await registry_test.test_scraper_start_stop()
        await registry_test.test_health_monitoring()
        print("✅ Scraper Registry tests passed")
        
        print("Testing CoinGecko Scraper...")
        coingecko_test = TestCoinGeckoScraper()
        await coingecko_test.test_scraper_creation()
        await coingecko_test.test_auth_headers()
        print("✅ CoinGecko Scraper tests passed")
        
        print("Testing Etherscan Scraper...")
        etherscan_test = TestEtherscanScraper()
        await etherscan_test.test_scraper_creation()
        await etherscan_test.test_auth_headers()
        await etherscan_test.test_base_params()
        print("✅ Etherscan Scraper tests passed")
        
        print("🎉 All Phase 2 tests passed!")
    
    asyncio.run(run_async_tests())