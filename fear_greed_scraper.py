"""
Fear & Greed Index API scraper for market sentiment data.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog

from alpha_grid.agents.scrapers.base_scraper import BaseScraper
from alpha_grid.core.config import config_manager


logger = structlog.get_logger(__name__)


class FearGreedScraper(BaseScraper):
    """Fear & Greed Index API scraper for cryptocurrency market sentiment."""
    
    def __init__(self):
        """Initialize Fear & Greed scraper."""
        super().__init__(
            name="FearGreed",
            rate_limit=config_manager.settings.fear_greed_rate_limit,
            api_key=None,  # No API key required
            base_url="https://api.alternative.me"
        )
        
        # Cache for reducing API calls
        self._sentiment_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 300  # 5 minutes
        
        self.logger = logger.bind(scraper="FearGreed")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers (none required for Fear & Greed API)."""
        return {}
    
    async def health_check(self) -> bool:
        """Check if Fear & Greed API is healthy."""
        try:
            current_data = await self.get_current_sentiment()
            return current_data is not None and "value" in current_data
        except Exception as e:
            self.logger.error("Health check failed", error=str(e))
            return False
    
    async def get_current_sentiment(self) -> Dict[str, Any]:
        """
        Get current Fear & Greed Index value.
        
        Returns:
            Current sentiment data with value and classification
        """
        try:
            # Check cache first
            cache_key = "current_sentiment"
            if self._is_cache_valid() and cache_key in self._sentiment_cache:
                self.logger.debug("Returning cached sentiment data")
                return self._sentiment_cache[cache_key]
            
            url = f"{self.base_url}/fng/"
            
            self.logger.debug("Fetching current Fear & Greed Index")
            
            response = await self._make_request(url)
            
            if not response or "data" not in response:
                raise ValueError("Invalid response format from Fear & Greed API")
            
            data = response["data"]
            if not data or len(data) == 0:
                raise ValueError("No data returned from Fear & Greed API")
            
            current_data = data[0]  # Most recent data point
            
            # Format the data
            sentiment_data = {
                "value": int(current_data.get("value", 0)),
                "value_classification": current_data.get("value_classification", "Unknown"),
                "timestamp": current_data.get("timestamp"),
                "time_until_update": current_data.get("time_until_update"),
                "readable_timestamp": self._format_timestamp(current_data.get("timestamp"))
            }
            
            # Add interpretation
            sentiment_data["interpretation"] = self._interpret_sentiment(sentiment_data["value"])
            
            # Cache the result
            self._sentiment_cache[cache_key] = sentiment_data
            self._cache_timestamp = datetime.now()
            
            self.logger.info(
                "Current sentiment fetched",
                value=sentiment_data["value"],
                classification=sentiment_data["value_classification"]
            )
            
            return sentiment_data
            
        except Exception as e:
            self.logger.error("Failed to get current sentiment", error=str(e))
            raise
    
    async def get_historical_sentiment(self, limit: int = 30) -> List[Dict[str, Any]]:
        """
        Get historical Fear & Greed Index values.
        
        Args:
            limit: Number of historical data points to return (max 100)
            
        Returns:
            List of historical sentiment data
        """
        try:
            url = f"{self.base_url}/fng/"
            params = {"limit": min(limit, 100)}  # API limit
            
            self.logger.debug("Fetching historical Fear & Greed Index", limit=limit)
            
            response = await self._make_request(url, params=params)
            
            if not response or "data" not in response:
                raise ValueError("Invalid response format from Fear & Greed API")
            
            data = response["data"]
            
            historical_data = []
            for item in data:
                sentiment_item = {
                    "value": int(item.get("value", 0)),
                    "value_classification": item.get("value_classification", "Unknown"),
                    "timestamp": item.get("timestamp"),
                    "readable_timestamp": self._format_timestamp(item.get("timestamp")),
                    "interpretation": self._interpret_sentiment(int(item.get("value", 0)))
                }
                historical_data.append(sentiment_item)
            
            self.logger.info("Historical sentiment fetched", count=len(historical_data))
            return historical_data
            
        except Exception as e:
            self.logger.error("Failed to get historical sentiment", error=str(e), limit=limit)
            raise
    
    async def get_sentiment_trend(self, days: int = 7) -> Dict[str, Any]:
        """
        Analyze sentiment trend over specified period.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Sentiment trend analysis
        """
        try:
            historical_data = await self.get_historical_sentiment(limit=days)
            
            if len(historical_data) < 2:
                return {"trend": "insufficient_data", "change": 0}
            
            # Calculate trend
            current_value = historical_data[0]["value"]  # Most recent
            oldest_value = historical_data[-1]["value"]  # Oldest in range
            
            change = current_value - oldest_value
            change_percentage = (change / oldest_value) * 100 if oldest_value > 0 else 0
            
            # Determine trend direction
            if abs(change) < 5:
                trend = "stable"
            elif change > 0:
                trend = "improving"  # Less fear, more greed
            else:
                trend = "declining"  # More fear, less greed
            
            # Calculate average
            values = [item["value"] for item in historical_data]
            average_value = sum(values) / len(values)
            
            # Calculate volatility (standard deviation)
            variance = sum((x - average_value) ** 2 for x in values) / len(values)
            volatility = variance ** 0.5
            
            trend_analysis = {
                "trend": trend,
                "change": change,
                "change_percentage": round(change_percentage, 2),
                "current_value": current_value,
                "period_start_value": oldest_value,
                "average_value": round(average_value, 1),
                "volatility": round(volatility, 1),
                "days_analyzed": len(historical_data),
                "interpretation": self._interpret_trend(trend, change, volatility)
            }
            
            self.logger.info(
                "Sentiment trend analyzed",
                trend=trend,
                change=change,
                days=days
            )
            
            return trend_analysis
            
        except Exception as e:
            self.logger.error("Failed to analyze sentiment trend", error=str(e), days=days)
            raise
    
    async def get_sentiment_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive sentiment summary.
        
        Returns:
            Complete sentiment analysis including current value, trend, and insights
        """
        try:
            # Get current sentiment
            current = await self.get_current_sentiment()
            
            # Get trend analysis
            trend_7d = await self.get_sentiment_trend(days=7)
            trend_30d = await self.get_sentiment_trend(days=30)
            
            summary = {
                "current": current,
                "trend_7d": trend_7d,
                "trend_30d": trend_30d,
                "market_phase": self._determine_market_phase(current["value"], trend_7d, trend_30d),
                "recommendation": self._generate_recommendation(current["value"], trend_7d),
                "last_updated": datetime.now().isoformat()
            }
            
            self.logger.info("Sentiment summary generated")
            return summary
            
        except Exception as e:
            self.logger.error("Failed to generate sentiment summary", error=str(e))
            raise
    
    def _format_timestamp(self, timestamp: str) -> str:
        """Format timestamp to readable string."""
        try:
            if timestamp:
                dt = datetime.fromtimestamp(int(timestamp))
                return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            pass
        return "Unknown"
    
    def _interpret_sentiment(self, value: int) -> str:
        """Interpret sentiment value into market condition."""
        if value <= 20:
            return "Extreme Fear - Potential buying opportunity"
        elif value <= 40:
            return "Fear - Market pessimism, consider accumulating"
        elif value <= 60:
            return "Neutral - Balanced market sentiment"
        elif value <= 80:
            return "Greed - Market optimism, consider taking profits"
        else:
            return "Extreme Greed - High risk, consider reducing exposure"
    
    def _interpret_trend(self, trend: str, change: int, volatility: float) -> str:
        """Interpret trend with additional context."""
        base_interpretation = {
            "improving": "Sentiment is becoming more positive",
            "declining": "Sentiment is becoming more negative", 
            "stable": "Sentiment remains relatively unchanged"
        }
        
        volatility_note = ""
        if volatility > 15:
            volatility_note = " with high volatility"
        elif volatility < 5:
            volatility_note = " with low volatility"
        
        return base_interpretation.get(trend, "Unknown trend") + volatility_note
    
    def _determine_market_phase(self, current_value: int, trend_7d: Dict, trend_30d: Dict) -> str:
        """Determine current market phase based on sentiment data."""
        if current_value <= 25 and trend_7d["trend"] == "declining":
            return "Capitulation"
        elif current_value <= 40 and trend_7d["trend"] == "improving":
            return "Recovery"
        elif 40 < current_value <= 60:
            return "Accumulation"
        elif 60 < current_value <= 75 and trend_7d["trend"] == "improving":
            return "Bull Market"
        elif current_value > 75:
            return "Euphoria"
        else:
            return "Transition"
    
    def _generate_recommendation(self, current_value: int, trend_7d: Dict) -> str:
        """Generate trading recommendation based on sentiment."""
        if current_value <= 25:
            return "Strong Buy - Extreme fear presents opportunity"
        elif current_value <= 40:
            return "Buy - Fear-driven selling may be overdone"
        elif 40 < current_value <= 60:
            return "Hold - Neutral sentiment, wait for clearer signals"
        elif 60 < current_value <= 80:
            return "Caution - Greed building, consider taking profits"
        else:
            return "Sell - Extreme greed, high risk of correction"
    
    def _is_cache_valid(self) -> bool:
        """Check if cache is still valid."""
        if self._cache_timestamp is None:
            return False

        return (datetime.now() - self._cache_timestamp).total_seconds() < self._cache_ttl

    async def get_fear_greed_index(self) -> Dict[str, Any]:
        """
        Get current Fear & Greed Index data.

        Returns:
            Current Fear & Greed Index with value and classification
        """
        try:
            # Use existing method to get current sentiment
            current_sentiment = await self.get_current_sentiment()

            # Format for agent consumption
            index_data = {
                'value': current_sentiment.get('value', 50),
                'classification': current_sentiment.get('value_classification', 'Neutral'),
                'timestamp': current_sentiment.get('timestamp', datetime.now().isoformat()),
                'previous_value': current_sentiment.get('previous_value', 50),
                'change': current_sentiment.get('value', 50) - current_sentiment.get('previous_value', 50)
            }

            self.logger.info("Fear & Greed Index retrieved", value=index_data['value'])
            return index_data

        except Exception as e:
            self.logger.error("Failed to get Fear & Greed Index", error=str(e))
            # Return fallback data
            return {
                'value': 50,
                'classification': 'Neutral',
                'timestamp': datetime.now().isoformat(),
                'previous_value': 50,
                'change': 0
            }
