"""
CoinGecko API scraper for market data and new coin listings.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog

from .base_scraper import BaseScraper
from alpha_grid.core.config import config_manager


logger = structlog.get_logger(__name__)


class CoinGeckoScraper(BaseScraper):
    """CoinGecko API scraper for cryptocurrency market data."""
    
    def __init__(self):
        """Initialize CoinGecko scraper."""
        api_key = config_manager.get_decrypted_key('coingecko_api_key')
        super().__init__(
            name="CoinGecko",
            rate_limit=config_manager.settings.coingecko_rate_limit,
            api_key=api_key,
            base_url="https://api.coingecko.com/api/v3"
        )
        
        # Cache for reducing API calls
        self._market_data_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 300  # 5 minutes
        
        self.logger = logger.bind(scraper="CoinGecko")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for CoinGecko API."""
        if self.api_key:
            return {"x-cg-demo-api-key": self.api_key}
        return {}
    
    async def health_check(self) -> bool:
        """Check if CoinGecko API is healthy."""
        try:
            url = f"{self.base_url}/ping"
            response = await self._make_request(url)
            return response.get("gecko_says") == "(V3) To the Moon!"
        except Exception as e:
            self.logger.error("Health check failed", error=str(e))
            return False
    
    async def get_market_data(
        self, 
        vs_currency: str = "usd",
        per_page: int = 100,
        page: int = 1,
        order: str = "market_cap_desc"
    ) -> List[Dict[str, Any]]:
        """
        Get market data for cryptocurrencies.
        
        Args:
            vs_currency: Target currency (default: usd)
            per_page: Number of results per page (max 250)
            page: Page number
            order: Sort order (market_cap_desc, volume_desc, etc.)
            
        Returns:
            List of coin market data
        """
        try:
            # Check cache first
            cache_key = f"market_data_{vs_currency}_{per_page}_{page}_{order}"
            if self._is_cache_valid() and cache_key in self._market_data_cache:
                self.logger.debug("Returning cached market data")
                return self._market_data_cache[cache_key]
            
            url = f"{self.base_url}/coins/markets"
            params = {
                "vs_currency": vs_currency,
                "order": order,
                "per_page": min(per_page, 250),  # API limit
                "page": page,
                "sparkline": False,
                "price_change_percentage": "1h,24h,7d"
            }
            
            self.logger.info(
                "Fetching market data",
                vs_currency=vs_currency,
                per_page=per_page,
                page=page
            )
            
            data = await self._make_request(url, params=params)
            
            # Cache the result
            self._market_data_cache[cache_key] = data
            self._cache_timestamp = datetime.now()
            
            self.logger.info(
                "Market data fetched successfully",
                coins_count=len(data)
            )
            
            return data
            
        except Exception as e:
            self.logger.error(
                "Failed to fetch market data",
                error=str(e),
                vs_currency=vs_currency,
                per_page=per_page
            )
            raise
    
    async def get_top_coins(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get top cryptocurrencies by market cap.
        
        Args:
            limit: Number of top coins to return
            
        Returns:
            List of top coins data
        """
        try:
            data = await self.get_market_data(per_page=limit, page=1)
            
            # Extract key information
            top_coins = []
            for coin in data:
                top_coins.append({
                    "id": coin.get("id"),
                    "symbol": coin.get("symbol", "").upper(),
                    "name": coin.get("name"),
                    "current_price": coin.get("current_price"),
                    "market_cap": coin.get("market_cap"),
                    "market_cap_rank": coin.get("market_cap_rank"),
                    "total_volume": coin.get("total_volume"),
                    "price_change_percentage_24h": coin.get("price_change_percentage_24h"),
                    "price_change_percentage_7d": coin.get("price_change_percentage_7d_in_currency"),
                    "circulating_supply": coin.get("circulating_supply"),
                    "total_supply": coin.get("total_supply"),
                    "last_updated": coin.get("last_updated")
                })
            
            self.logger.info("Top coins data processed", count=len(top_coins))
            return top_coins
            
        except Exception as e:
            self.logger.error("Failed to get top coins", error=str(e), limit=limit)
            raise
    
    async def get_coin_by_id(self, coin_id: str) -> Dict[str, Any]:
        """
        Get detailed information for a specific coin.
        
        Args:
            coin_id: CoinGecko coin ID
            
        Returns:
            Detailed coin information
        """
        try:
            url = f"{self.base_url}/coins/{coin_id}"
            params = {
                "localization": False,
                "tickers": False,
                "market_data": True,
                "community_data": False,
                "developer_data": False,
                "sparkline": False
            }
            
            self.logger.debug("Fetching coin details", coin_id=coin_id)
            
            data = await self._make_request(url, params=params)
            
            # Extract relevant market data
            market_data = data.get("market_data", {})
            
            coin_info = {
                "id": data.get("id"),
                "symbol": data.get("symbol", "").upper(),
                "name": data.get("name"),
                "description": data.get("description", {}).get("en", "")[:500],  # Truncate
                "homepage": data.get("links", {}).get("homepage", [None])[0],
                "blockchain_site": data.get("links", {}).get("blockchain_site", []),
                "current_price": market_data.get("current_price", {}).get("usd"),
                "market_cap": market_data.get("market_cap", {}).get("usd"),
                "market_cap_rank": market_data.get("market_cap_rank"),
                "total_volume": market_data.get("total_volume", {}).get("usd"),
                "circulating_supply": market_data.get("circulating_supply"),
                "total_supply": market_data.get("total_supply"),
                "max_supply": market_data.get("max_supply"),
                "ath": market_data.get("ath", {}).get("usd"),
                "ath_date": market_data.get("ath_date", {}).get("usd"),
                "atl": market_data.get("atl", {}).get("usd"),
                "atl_date": market_data.get("atl_date", {}).get("usd"),
                "price_change_24h": market_data.get("price_change_24h"),
                "price_change_percentage_24h": market_data.get("price_change_percentage_24h"),
                "price_change_percentage_7d": market_data.get("price_change_percentage_7d"),
                "price_change_percentage_30d": market_data.get("price_change_percentage_30d"),
                "last_updated": data.get("last_updated")
            }
            
            self.logger.debug("Coin details fetched successfully", coin_id=coin_id)
            return coin_info
            
        except Exception as e:
            self.logger.error("Failed to get coin details", error=str(e), coin_id=coin_id)
            raise
    
    async def get_global_data(self) -> Dict[str, Any]:
        """
        Get global cryptocurrency market data.
        
        Returns:
            Global market statistics
        """
        try:
            url = f"{self.base_url}/global"
            
            self.logger.debug("Fetching global market data")
            
            response = await self._make_request(url)
            data = response.get("data", {})
            
            global_data = {
                "total_market_cap": data.get("total_market_cap", {}).get("usd"),
                "total_volume": data.get("total_volume", {}).get("usd"),
                "market_cap_percentage": data.get("market_cap_percentage", {}),
                "market_cap_change_percentage_24h": data.get("market_cap_change_percentage_24h_usd"),
                "active_cryptocurrencies": data.get("active_cryptocurrencies"),
                "upcoming_icos": data.get("upcoming_icos"),
                "ongoing_icos": data.get("ongoing_icos"),
                "ended_icos": data.get("ended_icos"),
                "markets": data.get("markets"),
                "updated_at": data.get("updated_at")
            }
            
            self.logger.info("Global market data fetched successfully")
            return global_data
            
        except Exception as e:
            self.logger.error("Failed to get global data", error=str(e))
            raise
    
    async def search_coins(self, query: str) -> List[Dict[str, Any]]:
        """
        Search for coins by name or symbol.
        
        Args:
            query: Search query
            
        Returns:
            List of matching coins
        """
        try:
            url = f"{self.base_url}/search"
            params = {"query": query}
            
            self.logger.debug("Searching coins", query=query)
            
            response = await self._make_request(url, params=params)
            coins = response.get("coins", [])
            
            # Limit results and extract key info
            results = []
            for coin in coins[:20]:  # Limit to top 20 results
                results.append({
                    "id": coin.get("id"),
                    "name": coin.get("name"),
                    "symbol": coin.get("symbol", "").upper(),
                    "market_cap_rank": coin.get("market_cap_rank"),
                    "thumb": coin.get("thumb")
                })
            
            self.logger.debug("Coin search completed", query=query, results_count=len(results))
            return results
            
        except Exception as e:
            self.logger.error("Failed to search coins", error=str(e), query=query)
            raise
    
    def _is_cache_valid(self) -> bool:
        """Check if cache is still valid."""
        if self._cache_timestamp is None:
            return False
        
        return (datetime.now() - self._cache_timestamp).total_seconds() < self._cache_ttl
    
    async def get_trending_coins(self) -> List[Dict[str, Any]]:
        """
        Get trending coins (most searched).
        
        Returns:
            List of trending coins
        """
        try:
            url = f"{self.base_url}/search/trending"
            
            self.logger.debug("Fetching trending coins")
            
            response = await self._make_request(url)
            trending_coins = response.get("coins", [])
            
            results = []
            for item in trending_coins:
                coin = item.get("item", {})
                results.append({
                    "id": coin.get("id"),
                    "name": coin.get("name"),
                    "symbol": coin.get("symbol", "").upper(),
                    "market_cap_rank": coin.get("market_cap_rank"),
                    "thumb": coin.get("thumb"),
                    "score": coin.get("score")
                })
            
            self.logger.info("Trending coins fetched", count=len(results))
            return results
            
        except Exception as e:
            self.logger.error("Failed to get trending coins", error=str(e))
            raise
