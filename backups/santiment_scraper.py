"""
Santiment API scraper for social, development, and crowd data.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog
import json

from alpha_grid.agents.scrapers.base_scraper import BaseScraper
from alpha_grid.core.config import config_manager


logger = structlog.get_logger(__name__)


class SantimentScraper(BaseScraper):
    """Santiment API scraper for social, development, and crowd data."""
    
    def __init__(self):
        """Initialize Santiment scraper."""
        api_key = config_manager.get_decrypted_key('santiment_api_key')
        super().__init__(
            name="Santiment",
            rate_limit=config_manager.settings.santiment_rate_limit,
            api_key=api_key,
            base_url="https://api.santiment.net/graphql"
        )
        
        # Cache for reducing API calls
        self._social_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 1800  # 30 minutes for social data
        
        # Key metrics to track
        self.social_metrics = [
            "social_volume_total",
            "social_dominance",
            "sentiment_positive",
            "sentiment_negative",
            "sentiment_balance"
        ]
        
        self.dev_metrics = [
            "dev_activity",
            "github_activity",
            "dev_activity_contributors_count"
        ]
        
        self.logger = logger.bind(scraper="Santiment")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for Santiment API."""
        headers = {"Content-Type": "application/json"}
        if self.api_key:
            headers["Authorization"] = f"Apikey {self.api_key}"
        return headers
    
    async def _make_graphql_request(self, query: str, variables: Dict = None) -> Dict[str, Any]:
        """Make a GraphQL request to Santiment API."""
        payload = {"query": query}
        if variables:
            payload["variables"] = variables
        
        # Override the base _make_request to handle GraphQL
        import aiohttp
        
        headers = self._get_auth_headers()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "errors" in result:
                            raise Exception(f"GraphQL errors: {result['errors']}")
                        return result.get("data", {})
                    else:
                        raise Exception(f"HTTP {response.status}: {await response.text()}")
        except Exception as e:
            self.logger.error("GraphQL request failed", query=query[:100], error=str(e))
            raise
    
    async def health_check(self) -> bool:
        """Check if Santiment API is healthy."""
        try:
            query = """
            {
                __schema {
                    queryType {
                        name
                    }
                }
            }
            """
            response = await self._make_graphql_request(query)
            return "__schema" in response
        except Exception as e:
            self.logger.error("Health check failed", error=str(e))
            return False
    
    async def get_available_projects(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get list of available projects.
        
        Args:
            limit: Number of projects to return
            
        Returns:
            List of available projects
        """
        try:
            query = f"""
            {{
                allProjects(page: 1, pageSize: {limit}) {{
                    slug
                    name
                    ticker
                    description
                    marketcapUsd
                }}
            }}
            """
            
            self.logger.debug("Fetching available projects")
            
            response = await self._make_graphql_request(query)
            
            if "allProjects" not in response:
                raise ValueError("Invalid response format from projects API")
            
            projects = []
            for project in response["allProjects"]:
                project_info = {
                    "slug": project.get("slug"),
                    "name": project.get("name"),
                    "ticker": project.get("ticker"),
                    "description": project.get("description"),
                    "market_cap_usd": project.get("marketcapUsd")
                }
                projects.append(project_info)
            
            self.logger.info("Available projects fetched", count=len(projects))
            return projects
            
        except Exception as e:
            self.logger.error("Failed to get available projects", error=str(e))
            # Return simulated data for demonstration
            return self._get_simulated_projects(limit)
    
    async def get_social_data(self, slug: str, metrics: List[str] = None, 
                             from_date: str = None, to_date: str = None) -> Dict[str, Any]:
        """
        Get social data for a project.
        
        Args:
            slug: Project slug (e.g., "bitcoin", "ethereum")
            metrics: List of social metrics to fetch
            from_date: Start date in ISO format
            to_date: End date in ISO format
            
        Returns:
            Social data for the project
        """
        try:
            # Check cache first
            cache_key = f"social_{slug}_{metrics}_{from_date}_{to_date}"
            if self._is_cache_valid() and cache_key in self._social_cache:
                self.logger.debug("Returning cached social data", slug=slug)
                return self._social_cache[cache_key]
            
            if metrics is None:
                metrics = self.social_metrics[:3]  # Limit for free tier
            
            if from_date is None:
                from_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%dT%H:%M:%SZ")
            if to_date is None:
                to_date = datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")
            
            # Build GraphQL query for social metrics
            metric_queries = []
            for metric in metrics:
                metric_queries.append(f"""
                {metric}: getMetric(metric: "{metric}") {{
                    timeseriesData(
                        slug: "{slug}"
                        from: "{from_date}"
                        to: "{to_date}"
                        interval: "1d"
                    ) {{
                        datetime
                        value
                    }}
                }}
                """)
            
            query = "{" + "\n".join(metric_queries) + "}"
            
            self.logger.debug("Fetching social data", slug=slug, metrics=len(metrics))
            
            response = await self._make_graphql_request(query)
            
            social_data = {
                "slug": slug,
                "from_date": from_date,
                "to_date": to_date,
                "metrics": {},
                "summary": {}
            }
            
            # Process each metric
            for metric in metrics:
                if metric in response:
                    timeseries = response[metric].get("timeseriesData", [])
                    
                    # Extract values and calculate summary stats
                    values = [float(point.get("value", 0)) for point in timeseries if point.get("value")]
                    
                    social_data["metrics"][metric] = {
                        "timeseries": timeseries,
                        "latest_value": values[-1] if values else 0,
                        "average": sum(values) / len(values) if values else 0,
                        "max_value": max(values) if values else 0,
                        "min_value": min(values) if values else 0,
                        "data_points": len(values)
                    }
            
            # Generate summary
            social_data["summary"] = self._generate_social_summary(social_data["metrics"])
            
            # Cache the result
            self._social_cache[cache_key] = social_data
            self._cache_timestamp = datetime.now()
            
            self.logger.info("Social data fetched", slug=slug, metrics=len(metrics))
            return social_data
            
        except Exception as e:
            self.logger.error("Failed to get social data", slug=slug, error=str(e))
            # Return simulated data for demonstration
            return self._get_simulated_social_data(slug, metrics)
    
    async def get_dev_activity(self, slug: str, from_date: str = None, to_date: str = None) -> Dict[str, Any]:
        """
        Get development activity data for a project.
        
        Args:
            slug: Project slug
            from_date: Start date in ISO format
            to_date: End date in ISO format
            
        Returns:
            Development activity data
        """
        try:
            if from_date is None:
                from_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
            if to_date is None:
                to_date = datetime.now().strftime("%Y-%m-%d")
            
            query = f"""
            {{
                getMetric(metric: "dev_activity") {{
                    timeseriesData(
                        slug: "{slug}"
                        from: "{from_date}"
                        to: "{to_date}"
                        interval: "1d"
                    ) {{
                        datetime
                        value
                    }}
                }}
            }}
            """
            
            self.logger.debug("Fetching dev activity", slug=slug)
            
            response = await self._make_graphql_request(query)
            
            if "getMetric" not in response:
                raise ValueError("Invalid response format from dev activity API")
            
            timeseries = response["getMetric"].get("timeseriesData", [])
            values = [float(point.get("value", 0)) for point in timeseries if point.get("value")]
            
            dev_data = {
                "slug": slug,
                "from_date": from_date,
                "to_date": to_date,
                "timeseries": timeseries,
                "latest_activity": values[-1] if values else 0,
                "average_activity": sum(values) / len(values) if values else 0,
                "max_activity": max(values) if values else 0,
                "total_commits": sum(values) if values else 0,
                "active_days": len([v for v in values if v > 0]),
                "data_points": len(values),
                "activity_trend": self._calculate_trend(values) if len(values) > 1 else "stable"
            }
            
            self.logger.info("Dev activity fetched", slug=slug, commits=dev_data["total_commits"])
            return dev_data
            
        except Exception as e:
            self.logger.error("Failed to get dev activity", slug=slug, error=str(e))
            # Return simulated data for demonstration
            return self._get_simulated_dev_activity(slug)
    
    async def get_social_trends(self, limit: int = 10) -> Dict[str, Any]:
        """
        Get trending projects by social volume.
        
        Args:
            limit: Number of trending projects to return
            
        Returns:
            Social trends data
        """
        try:
            # Get trending projects (simplified query for free tier)
            query = f"""
            {{
                allProjects(page: 1, pageSize: {limit}) {{
                    slug
                    name
                    ticker
                    marketcapUsd
                }}
            }}
            """
            
            self.logger.debug("Fetching social trends")
            
            response = await self._make_graphql_request(query)
            
            if "allProjects" not in response:
                raise ValueError("Invalid response format from trends API")
            
            trends_data = {
                "trending_projects": [],
                "analysis_period": "24h",
                "total_projects": len(response["allProjects"]),
                "last_updated": datetime.now().isoformat()
            }
            
            # Process trending projects
            for project in response["allProjects"][:limit]:
                trend_info = {
                    "slug": project.get("slug"),
                    "name": project.get("name"),
                    "ticker": project.get("ticker"),
                    "market_cap_usd": project.get("marketcapUsd", 0),
                    "social_score": self._calculate_simulated_social_score(project.get("slug", "")),
                    "trend_direction": "up"  # Simulated for demo
                }
                trends_data["trending_projects"].append(trend_info)
            
            # Sort by social score (simulated)
            trends_data["trending_projects"].sort(key=lambda x: x["social_score"], reverse=True)
            
            self.logger.info("Social trends fetched", count=len(trends_data["trending_projects"]))
            return trends_data
            
        except Exception as e:
            self.logger.error("Failed to get social trends", error=str(e))
            # Return simulated data for demonstration
            return self._get_simulated_social_trends(limit)
    
    async def get_sentiment_analysis(self, slugs: List[str] = None) -> Dict[str, Any]:
        """
        Get sentiment analysis for multiple projects.
        
        Args:
            slugs: List of project slugs to analyze
            
        Returns:
            Sentiment analysis data
        """
        try:
            if slugs is None:
                slugs = ["bitcoin", "ethereum"]
            
            sentiment_data = {
                "projects": {},
                "overall_sentiment": "neutral",
                "sentiment_insights": "",
                "last_updated": datetime.now().isoformat()
            }
            
            for slug in slugs:
                try:
                    # Get social data for sentiment analysis
                    social_data = await self.get_social_data(slug, ["sentiment_balance", "social_volume_total"])
                    
                    sentiment_balance = social_data["metrics"].get("sentiment_balance", {}).get("latest_value", 0)
                    social_volume = social_data["metrics"].get("social_volume_total", {}).get("latest_value", 0)
                    
                    sentiment_data["projects"][slug] = {
                        "sentiment_balance": sentiment_balance,
                        "social_volume": social_volume,
                        "sentiment_label": self._classify_sentiment(sentiment_balance),
                        "volume_level": self._classify_volume(social_volume)
                    }
                    
                    # Rate limiting between projects
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    self.logger.warning("Failed to get sentiment for project", slug=slug, error=str(e))
                    continue
            
            # Generate overall insights
            sentiment_data["sentiment_insights"] = self._generate_sentiment_insights(sentiment_data["projects"])
            sentiment_data["overall_sentiment"] = self._calculate_overall_sentiment(sentiment_data["projects"])
            
            self.logger.info("Sentiment analysis completed", projects=len(sentiment_data["projects"]))
            return sentiment_data
            
        except Exception as e:
            self.logger.error("Failed to generate sentiment analysis", error=str(e))
            raise
    
    def _get_simulated_projects(self, limit: int) -> List[Dict[str, Any]]:
        """Get simulated projects for demonstration."""
        projects = [
            {"slug": "bitcoin", "name": "Bitcoin", "ticker": "BTC", "description": "Digital currency", "market_cap_usd": 1900000000000},
            {"slug": "ethereum", "name": "Ethereum", "ticker": "ETH", "description": "Smart contract platform", "market_cap_usd": 420000000000},
            {"slug": "cardano", "name": "Cardano", "ticker": "ADA", "description": "Blockchain platform", "market_cap_usd": 35000000000},
            {"slug": "solana", "name": "Solana", "ticker": "SOL", "description": "High-performance blockchain", "market_cap_usd": 45000000000},
            {"slug": "polkadot", "name": "Polkadot", "ticker": "DOT", "description": "Multi-chain protocol", "market_cap_usd": 25000000000}
        ]
        return projects[:limit]
    
    def _get_simulated_social_data(self, slug: str, metrics: List[str]) -> Dict[str, Any]:
        """Get simulated social data for demonstration."""
        base_values = {
            "bitcoin": {"social_volume_total": 15000, "sentiment_balance": 0.15, "social_dominance": 25.5},
            "ethereum": {"social_volume_total": 12000, "sentiment_balance": 0.08, "social_dominance": 18.2},
            "cardano": {"social_volume_total": 3500, "sentiment_balance": 0.22, "social_dominance": 5.1}
        }
        
        project_base = base_values.get(slug, {"social_volume_total": 1000, "sentiment_balance": 0.0, "social_dominance": 1.0})
        
        social_data = {
            "slug": slug,
            "from_date": (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d"),
            "to_date": datetime.now().strftime("%Y-%m-%d"),
            "metrics": {},
            "summary": {}
        }
        
        for metric in (metrics or self.social_metrics[:3]):
            base_value = project_base.get(metric, 1000)
            social_data["metrics"][metric] = {
                "latest_value": base_value,
                "average": base_value * 0.9,
                "max_value": base_value * 1.2,
                "min_value": base_value * 0.7,
                "data_points": 7
            }
        
        social_data["summary"] = self._generate_social_summary(social_data["metrics"])
        return social_data
    
    def _get_simulated_dev_activity(self, slug: str) -> Dict[str, Any]:
        """Get simulated dev activity for demonstration."""
        base_activity = {"bitcoin": 45, "ethereum": 120, "cardano": 85}.get(slug, 30)
        
        return {
            "slug": slug,
            "from_date": (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"),
            "to_date": datetime.now().strftime("%Y-%m-%d"),
            "latest_activity": base_activity,
            "average_activity": base_activity * 0.9,
            "max_activity": base_activity * 1.5,
            "total_commits": base_activity * 30,
            "active_days": 25,
            "data_points": 30,
            "activity_trend": "increasing"
        }
    
    def _get_simulated_social_trends(self, limit: int) -> Dict[str, Any]:
        """Get simulated social trends for demonstration."""
        trending = [
            {"slug": "bitcoin", "name": "Bitcoin", "ticker": "BTC", "market_cap_usd": 1900000000000, "social_score": 95, "trend_direction": "up"},
            {"slug": "ethereum", "name": "Ethereum", "ticker": "ETH", "market_cap_usd": 420000000000, "social_score": 88, "trend_direction": "up"},
            {"slug": "solana", "name": "Solana", "ticker": "SOL", "market_cap_usd": 45000000000, "social_score": 72, "trend_direction": "up"},
            {"slug": "cardano", "name": "Cardano", "ticker": "ADA", "market_cap_usd": 35000000000, "social_score": 65, "trend_direction": "stable"},
            {"slug": "polkadot", "name": "Polkadot", "ticker": "DOT", "market_cap_usd": 25000000000, "social_score": 58, "trend_direction": "down"}
        ]
        
        return {
            "trending_projects": trending[:limit],
            "analysis_period": "24h",
            "total_projects": len(trending),
            "last_updated": datetime.now().isoformat()
        }
    
    def _calculate_simulated_social_score(self, slug: str) -> int:
        """Calculate simulated social score based on slug."""
        scores = {"bitcoin": 95, "ethereum": 88, "solana": 72, "cardano": 65, "polkadot": 58}
        return scores.get(slug, 50)

    async def get_social_metrics(self, symbol: str) -> Dict[str, Any]:
        """
        Get social metrics for a cryptocurrency symbol.

        Args:
            symbol: Cryptocurrency symbol (e.g., 'btc', 'eth')

        Returns:
            Social metrics data
        """
        try:
            # Convert symbol to slug format
            slug_map = {
                'btc': 'bitcoin',
                'eth': 'ethereum',
                'sol': 'solana',
                'ada': 'cardano',
                'dot': 'polkadot'
            }
            slug = slug_map.get(symbol.lower(), symbol.lower())

            # Get social data using existing method
            social_data = await self.get_social_data(slug, self.social_metrics[:3])

            # Format for agent consumption
            metrics = {
                'social_volume': social_data.get('metrics', {}).get('social_volume_total', {}).get('latest_value', 0),
                'sentiment_score': social_data.get('metrics', {}).get('sentiment_balance', {}).get('latest_value', 0),
                'social_dominance': social_data.get('metrics', {}).get('social_dominance', {}).get('latest_value', 0),
                'trending': social_data.get('summary', {}).get('trend_direction', 'stable'),
                'last_updated': social_data.get('last_updated', datetime.now().isoformat())
            }

            self.logger.info("Social metrics retrieved", symbol=symbol, slug=slug)
            return metrics

        except Exception as e:
            self.logger.error("Failed to get social metrics", symbol=symbol, error=str(e))
            # Return fallback data
            return {
                'social_volume': 1000,
                'sentiment_score': 0.1,
                'social_dominance': 2.5,
                'trending': 'stable',
                'last_updated': datetime.now().isoformat()
            }
    
    def _generate_social_summary(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary from social metrics."""
        summary = {"total_metrics": len(metrics), "insights": []}
        
        for metric_name, data in metrics.items():
            latest = data.get("latest_value", 0)
            average = data.get("average", 0)
            
            if latest > average * 1.2:
                summary["insights"].append(f"{metric_name} significantly above average")
            elif latest < average * 0.8:
                summary["insights"].append(f"{metric_name} below average")
        
        return summary
    
    def _classify_sentiment(self, sentiment_balance: float) -> str:
        """Classify sentiment based on balance."""
        if sentiment_balance > 0.1:
            return "positive"
        elif sentiment_balance < -0.1:
            return "negative"
        else:
            return "neutral"
    
    def _classify_volume(self, volume: float) -> str:
        """Classify social volume level."""
        if volume > 10000:
            return "high"
        elif volume > 5000:
            return "medium"
        else:
            return "low"
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction from values."""
        if len(values) < 2:
            return "stable"
        
        recent_avg = sum(values[-3:]) / min(3, len(values))
        older_avg = sum(values[:3]) / min(3, len(values))
        
        if recent_avg > older_avg * 1.1:
            return "increasing"
        elif recent_avg < older_avg * 0.9:
            return "decreasing"
        else:
            return "stable"
    
    def _generate_sentiment_insights(self, projects: Dict[str, Any]) -> str:
        """Generate insights from sentiment data."""
        insights = []
        
        positive_count = sum(1 for p in projects.values() if p.get("sentiment_label") == "positive")
        negative_count = sum(1 for p in projects.values() if p.get("sentiment_label") == "negative")
        
        if positive_count > negative_count:
            insights.append("Overall positive sentiment in tracked projects")
        elif negative_count > positive_count:
            insights.append("Overall negative sentiment in tracked projects")
        else:
            insights.append("Mixed sentiment across tracked projects")
        
        high_volume_count = sum(1 for p in projects.values() if p.get("volume_level") == "high")
        if high_volume_count > 0:
            insights.append(f"{high_volume_count} projects with high social volume")
        
        return " | ".join(insights) if insights else "Standard sentiment levels"
    
    def _calculate_overall_sentiment(self, projects: Dict[str, Any]) -> str:
        """Calculate overall sentiment across projects."""
        sentiments = [p.get("sentiment_label", "neutral") for p in projects.values()]
        
        positive_count = sentiments.count("positive")
        negative_count = sentiments.count("negative")
        
        if positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"
    
    def _is_cache_valid(self) -> bool:
        """Check if cache is still valid."""
        if self._cache_timestamp is None:
            return False
        
        return (datetime.now() - self._cache_timestamp).total_seconds() < self._cache_ttl
