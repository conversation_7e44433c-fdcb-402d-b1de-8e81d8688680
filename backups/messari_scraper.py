"""
Messari API scraper for fundamental crypto data and research.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog

from alpha_grid.agents.scrapers.base_scraper import BaseScraper
from alpha_grid.core.config import config_manager


logger = structlog.get_logger(__name__)


class MessariScraper(BaseScraper):
    """Messari API scraper for fundamental cryptocurrency data."""
    
    def __init__(self):
        """Initialize Messari scraper."""
        api_key = config_manager.get_decrypted_key('messari_api_key')
        super().__init__(
            name="<PERSON><PERSON><PERSON>",
            rate_limit=config_manager.settings.messari_rate_limit,
            api_key=api_key,
            base_url="https://api.messari.io"
        )
        
        # Cache for reducing API calls
        self._fundamental_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 600  # 10 minutes for fundamental data
        
        self.logger = logger.bind(scraper="Me<PERSON>ri")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for Messari API."""
        return {
            "x-messari-api-key": self.api_key
        }
    
    async def health_check(self) -> bool:
        """Check if Messari API is healthy."""
        try:
            # Test with a simple assets list query
            url = f"{self.base_url}/metrics/v2/assets"
            params = {"limit": 1}
            
            response = await self._make_request(url, params=params)
            return "data" in response and isinstance(response["data"], list)
        except Exception as e:
            self.logger.error("Health check failed", error=str(e))
            return False
    
    async def get_asset_fundamentals(self, symbols: List[str] = None) -> List[Dict[str, Any]]:
        """
        Get fundamental data for specified assets.
        
        Args:
            symbols: List of asset symbols (if None, gets top assets)
            
        Returns:
            List of asset fundamental data
        """
        try:
            if symbols is None:
                symbols = ["BTC", "ETH", "SOL", "ADA", "AVAX", "DOT", "LINK", "UNI", "MATIC", "ATOM"]
            
            fundamentals = []
            
            for symbol in symbols[:10]:  # Limit to avoid rate limits
                try:
                    url = f"{self.base_url}/metrics/v2/assets/{symbol.lower()}"
                    
                    self.logger.debug("Fetching asset fundamentals", symbol=symbol)
                    
                    response = await self._make_request(url)
                    
                    if "data" in response and response["data"]:
                        asset_data = response["data"]
                        
                        formatted_data = self._format_asset_fundamentals(asset_data, symbol)
                        fundamentals.append(formatted_data)
                    
                    # Rate limiting
                    await asyncio.sleep(0.3)
                    
                except Exception as e:
                    self.logger.warning("Failed to get fundamentals for asset", symbol=symbol, error=str(e))
                    continue
            
            self.logger.info("Asset fundamentals fetched", count=len(fundamentals))
            return fundamentals
            
        except Exception as e:
            self.logger.error("Failed to get asset fundamentals", error=str(e))
            raise
    
    async def get_trending_assets(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get trending assets based on various metrics.
        
        Args:
            limit: Number of trending assets to return
            
        Returns:
            List of trending asset data
        """
        try:
            url = f"{self.base_url}/metrics/v2/assets"
            params = {
                "limit": limit,
                "sort": "market_cap_rank",
                "order": "asc"
            }
            
            self.logger.debug("Fetching trending assets")
            
            response = await self._make_request(url, params=params)
            
            if "data" not in response:
                raise ValueError("Invalid response from trending assets API")
            
            trending_assets = []
            for asset in response["data"]:
                formatted_asset = {
                    "symbol": asset.get("symbol"),
                    "name": asset.get("name"),
                    "slug": asset.get("slug"),
                    "market_cap_rank": asset.get("market_cap_rank"),
                    "categories": asset.get("categories", []),
                    "sectors": asset.get("sectors", []),
                    "tags": asset.get("tags", []),
                    "coverage": asset.get("coverage", {}),
                    "has_diligence": asset.get("coverage", {}).get("diligence", False),
                    "has_intel": asset.get("coverage", {}).get("intel", False),
                    "has_token_unlocks": asset.get("coverage", {}).get("token_unlocks", False)
                }
                trending_assets.append(formatted_asset)
            
            self.logger.info("Trending assets fetched", count=len(trending_assets))
            return trending_assets
            
        except Exception as e:
            self.logger.error("Failed to get trending assets", error=str(e))
            raise
    
    async def get_research_reports(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get latest research reports.
        
        Args:
            limit: Number of reports to return
            
        Returns:
            List of research report data
        """
        try:
            url = f"{self.base_url}/research/reports"
            params = {"limit": limit}
            
            self.logger.debug("Fetching research reports")
            
            response = await self._make_request(url, params=params)
            
            if "data" not in response:
                raise ValueError("Invalid response from research reports API")
            
            reports = []
            for report in response["data"]:
                formatted_report = {
                    "id": report.get("id"),
                    "title": report.get("title"),
                    "subtitle": report.get("subtitle"),
                    "author": report.get("author"),
                    "published_at": report.get("published_at"),
                    "readable_published_at": self._format_timestamp(report.get("published_at")),
                    "tags": report.get("tags", []),
                    "asset_symbols": report.get("asset_symbols", []),
                    "url": report.get("url"),
                    "summary": report.get("summary", "")[:200] if report.get("summary") else ""  # Truncate
                }
                reports.append(formatted_report)
            
            self.logger.info("Research reports fetched", count=len(reports))
            return reports
            
        except Exception as e:
            self.logger.error("Failed to get research reports", error=str(e))
            raise
    
    async def get_token_unlocks(self, symbols: List[str] = None) -> List[Dict[str, Any]]:
        """
        Get upcoming token unlock events.
        
        Args:
            symbols: List of asset symbols to check
            
        Returns:
            List of token unlock data
        """
        try:
            if symbols is None:
                symbols = ["SOL", "ADA", "AVAX", "DOT", "LINK", "UNI", "MATIC", "ATOM"]
            
            unlocks = []
            
            for symbol in symbols[:5]:  # Limit to avoid rate limits
                try:
                    url = f"{self.base_url}/token-unlocks/unlock-events"
                    params = {
                        "asset_symbol": symbol,
                        "limit": 5
                    }
                    
                    self.logger.debug("Fetching token unlocks", symbol=symbol)
                    
                    response = await self._make_request(url, params=params)
                    
                    if "data" in response and response["data"]:
                        for unlock in response["data"]:
                            formatted_unlock = {
                                "asset_symbol": symbol,
                                "unlock_date": unlock.get("unlock_date"),
                                "readable_unlock_date": self._format_timestamp(unlock.get("unlock_date")),
                                "amount": unlock.get("amount"),
                                "amount_usd": unlock.get("amount_usd"),
                                "percentage_of_supply": unlock.get("percentage_of_supply"),
                                "category": unlock.get("category"),
                                "description": unlock.get("description", "")
                            }
                            unlocks.append(formatted_unlock)
                    
                    await asyncio.sleep(0.3)
                    
                except Exception as e:
                    self.logger.warning("Failed to get unlocks for asset", symbol=symbol, error=str(e))
                    continue
            
            # Sort by unlock date
            unlocks.sort(key=lambda x: x.get("unlock_date", ""), reverse=False)
            
            self.logger.info("Token unlocks fetched", count=len(unlocks))
            return unlocks
            
        except Exception as e:
            self.logger.error("Failed to get token unlocks", error=str(e))
            raise
    
    async def get_news_feed(self, limit: int = 15) -> List[Dict[str, Any]]:
        """
        Get latest crypto news from Messari.
        
        Args:
            limit: Number of news items to return
            
        Returns:
            List of news data
        """
        try:
            url = f"{self.base_url}/news/feed"
            params = {"limit": limit}
            
            self.logger.debug("Fetching news feed")
            
            response = await self._make_request(url, params=params)
            
            if "data" not in response:
                raise ValueError("Invalid response from news feed API")
            
            news_items = []
            for item in response["data"]:
                formatted_item = {
                    "id": item.get("id"),
                    "title": item.get("title"),
                    "content": item.get("content", "")[:300] if item.get("content") else "",  # Truncate
                    "published_at": item.get("published_at"),
                    "readable_published_at": self._format_timestamp(item.get("published_at")),
                    "url": item.get("url"),
                    "source": item.get("source", {}).get("name", "Unknown"),
                    "asset_symbols": item.get("asset_symbols", []),
                    "tags": item.get("tags", [])
                }
                news_items.append(formatted_item)
            
            self.logger.info("News feed fetched", count=len(news_items))
            return news_items
            
        except Exception as e:
            self.logger.error("Failed to get news feed", error=str(e))
            raise
    
    async def get_fundamental_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive fundamental analysis summary.
        
        Returns:
            Complete fundamental analysis
        """
        try:
            # Get all fundamental data
            fundamentals = await self.get_asset_fundamentals()
            trending = await self.get_trending_assets(limit=10)
            research = await self.get_research_reports(limit=5)
            unlocks = await self.get_token_unlocks()
            news = await self.get_news_feed(limit=10)
            
            # Calculate insights
            total_market_cap = sum(f.get("market_cap", 0) for f in fundamentals if f.get("market_cap"))
            avg_market_cap = total_market_cap / len(fundamentals) if fundamentals else 0
            
            upcoming_unlocks = [u for u in unlocks if self._is_upcoming_unlock(u.get("unlock_date"))]
            
            summary = {
                "fundamentals": {
                    "asset_count": len(fundamentals),
                    "total_market_cap": total_market_cap,
                    "average_market_cap": avg_market_cap,
                    "top_assets": fundamentals[:5]
                },
                "trending_assets": {
                    "count": len(trending),
                    "top_trending": trending[:5]
                },
                "research_insights": {
                    "report_count": len(research),
                    "latest_reports": research[:3]
                },
                "token_unlocks": {
                    "total_upcoming": len(upcoming_unlocks),
                    "upcoming_unlocks": upcoming_unlocks[:5]
                },
                "news_sentiment": {
                    "news_count": len(news),
                    "latest_news": news[:5]
                },
                "market_insights": self._generate_market_insights(fundamentals, trending, unlocks),
                "last_updated": datetime.now().isoformat()
            }
            
            self.logger.info("Fundamental summary generated")
            return summary
            
        except Exception as e:
            self.logger.error("Failed to generate fundamental summary", error=str(e))
            raise
    
    def _format_asset_fundamentals(self, asset_data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """Format asset fundamental data into standardized structure."""
        return {
            "symbol": symbol,
            "name": asset_data.get("name"),
            "slug": asset_data.get("slug"),
            "market_cap": asset_data.get("market_cap"),
            "market_cap_rank": asset_data.get("market_cap_rank"),
            "categories": asset_data.get("categories", []),
            "sectors": asset_data.get("sectors", []),
            "tags": asset_data.get("tags", []),
            "coverage": asset_data.get("coverage", {}),
            "has_fundamentals": bool(asset_data.get("coverage", {}).get("diligence")),
            "last_updated": datetime.now().isoformat()
        }
    
    def _format_timestamp(self, timestamp_str: str) -> str:
        """Format timestamp string to readable format."""
        try:
            if timestamp_str:
                # Handle ISO format
                dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            pass
        return "Unknown"
    
    def _is_upcoming_unlock(self, unlock_date: str) -> bool:
        """Check if unlock date is in the future."""
        try:
            if unlock_date:
                unlock_dt = datetime.fromisoformat(unlock_date.replace('Z', '+00:00'))
                return unlock_dt > datetime.now()
        except (ValueError, TypeError):
            pass
        return False
    
    def _generate_market_insights(self, fundamentals: List, trending: List, unlocks: List) -> str:
        """Generate market insights based on fundamental data."""
        insights = []
        
        if fundamentals:
            high_cap_assets = [f for f in fundamentals if f.get("market_cap", 0) > 10_000_000_000]
            insights.append(f"{len(high_cap_assets)} assets with >$10B market cap")
        
        if trending:
            defi_assets = [t for t in trending if "DeFi" in t.get("categories", [])]
            insights.append(f"{len(defi_assets)} DeFi assets in trending")
        
        if unlocks:
            upcoming_count = len([u for u in unlocks if self._is_upcoming_unlock(u.get("unlock_date"))])
            insights.append(f"{upcoming_count} upcoming token unlocks")
        
        return " | ".join(insights) if insights else "Limited fundamental data available"
