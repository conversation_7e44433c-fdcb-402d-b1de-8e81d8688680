"""
DexScreener API scraper for DEX trading data and new token detection.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog

from .base_scraper import BaseScraper
from core.config import config_manager


logger = structlog.get_logger(__name__)


class DexScreenerScraper(BaseScraper):
    """DexScreener API scraper for DEX trading data and new tokens."""
    
    def __init__(self):
        """Initialize DexScreener scraper."""
        super().__init__(
            name="DexScreener",
            rate_limit=config_manager.settings.dexscreener_rate_limit,
            api_key=None,  # No API key required
            base_url="https://api.dexscreener.com"
        )
        
        # Cache for reducing API calls
        self._pairs_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 60  # 1 minute for DEX data
        
        self.logger = logger.bind(scraper="DexScreener")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers (none required for DexScreener)."""
        return {}
    
    async def health_check(self) -> bool:
        """Check if DexScreener API is healthy."""
        try:
            # Test with a simple search query
            url = f"{self.base_url}/latest/dex/search"
            params = {"q": "ETH"}
            response = await self._make_request(url, params=params)
            return "pairs" in response and isinstance(response["pairs"], list)
        except Exception as e:
            self.logger.error("Health check failed", error=str(e))
            return False
    
    async def get_trending_pairs(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get trending DEX pairs.
        
        Args:
            limit: Number of pairs to return
            
        Returns:
            List of trending DEX pairs
        """
        try:
            # DexScreener doesn't have a direct trending endpoint
            # We'll search for popular tokens to get trending pairs
            popular_tokens = ["ETH", "BTC", "USDC", "USDT", "BNB"]
            all_pairs = []
            
            for token in popular_tokens:
                try:
                    url = f"{self.base_url}/latest/dex/search"
                    params = {"q": token}
                    
                    self.logger.debug("Searching for pairs", token=token)
                    
                    response = await self._make_request(url, params=params)
                    pairs = response.get("pairs", [])
                    
                    # Take top pairs for each token
                    for pair in pairs[:5]:  # Top 5 per token
                        if self._is_valid_pair(pair):
                            all_pairs.append(self._format_pair_data(pair))
                    
                    # Small delay between requests
                    await asyncio.sleep(0.25)
                    
                except Exception as e:
                    self.logger.warning("Failed to get pairs for token", token=token, error=str(e))
                    continue
            
            # Sort by volume and return top pairs
            sorted_pairs = sorted(
                all_pairs, 
                key=lambda x: x.get('volume_24h', 0), 
                reverse=True
            )
            
            result = sorted_pairs[:limit]
            self.logger.info("Trending pairs fetched", count=len(result))
            
            return result
            
        except Exception as e:
            self.logger.error("Failed to get trending pairs", error=str(e))
            raise
    
    async def get_new_pairs(self, hours: int = 24) -> List[Dict[str, Any]]:
        """
        Get newly created pairs by searching for recent high-activity pairs.

        Args:
            hours: Look for pairs created in the last N hours

        Returns:
            List of new DEX pairs
        """
        try:
            # Alternative approach: search for pairs and filter by creation time
            # This is more reliable than token profiles endpoint

            # Search for various tokens to find new pairs
            search_terms = ["pump", "new", "launch", "gem", "moon"]
            all_pairs = []

            for term in search_terms:
                try:
                    pairs = await self.search_pairs(term, limit=10)
                    all_pairs.extend(pairs)
                    await asyncio.sleep(0.25)  # Rate limiting
                except Exception as e:
                    self.logger.warning("Failed to search for term", term=term, error=str(e))
                    continue

            # Filter by creation time
            cutoff_time = datetime.now() - timedelta(hours=hours)
            new_pairs = []

            for pair in all_pairs:
                pair_created = pair.get("pair_created_at")
                if pair_created and pair_created > 0:
                    # Convert timestamp to datetime
                    created_time = datetime.fromtimestamp(pair_created / 1000)

                    if created_time > cutoff_time:
                        new_pairs.append(pair)

            # Remove duplicates based on pair address
            unique_pairs = {}
            for pair in new_pairs:
                pair_addr = pair.get("pair_address")
                if pair_addr and pair_addr not in unique_pairs:
                    unique_pairs[pair_addr] = pair

            # Sort by creation time (newest first)
            sorted_pairs = sorted(
                unique_pairs.values(),
                key=lambda x: x.get('pair_created_at', 0),
                reverse=True
            )

            result = sorted_pairs[:10]  # Limit to top 10
            self.logger.info("New pairs fetched", count=len(result), hours=hours)

            return result

        except Exception as e:
            self.logger.error("Failed to get new pairs", error=str(e))
            raise
    
    async def get_token_pairs(self, chain_id: str, token_address: str) -> List[Dict[str, Any]]:
        """
        Get pairs for a specific token.
        
        Args:
            chain_id: Blockchain ID (e.g., "ethereum", "bsc")
            token_address: Token contract address
            
        Returns:
            List of pairs for the token
        """
        try:
            url = f"{self.base_url}/latest/dex/tokens/{chain_id}/{token_address}"
            
            self.logger.debug("Fetching token pairs", chain_id=chain_id, token_address=token_address)
            
            response = await self._make_request(url)
            pairs = response.get("pairs", [])
            
            formatted_pairs = []
            for pair in pairs:
                if self._is_valid_pair(pair):
                    formatted_pairs.append(self._format_pair_data(pair))
            
            self.logger.debug("Token pairs fetched", count=len(formatted_pairs))
            return formatted_pairs
            
        except Exception as e:
            self.logger.error(
                "Failed to get token pairs", 
                error=str(e), 
                chain_id=chain_id, 
                token_address=token_address
            )
            raise
    
    async def get_pair_data(self, chain_id: str, pair_address: str) -> Dict[str, Any]:
        """
        Get detailed data for a specific pair.
        
        Args:
            chain_id: Blockchain ID
            pair_address: Pair contract address
            
        Returns:
            Detailed pair information
        """
        try:
            url = f"{self.base_url}/latest/dex/pairs/{chain_id}/{pair_address}"
            
            self.logger.debug("Fetching pair data", chain_id=chain_id, pair_address=pair_address)
            
            response = await self._make_request(url)
            pairs = response.get("pairs", [])
            
            if not pairs:
                raise ValueError(f"No pair found for {chain_id}/{pair_address}")
            
            pair_data = self._format_pair_data(pairs[0])
            self.logger.debug("Pair data fetched successfully")
            
            return pair_data
            
        except Exception as e:
            self.logger.error(
                "Failed to get pair data", 
                error=str(e), 
                chain_id=chain_id, 
                pair_address=pair_address
            )
            raise
    
    async def search_pairs(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Search for pairs by token name or symbol.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of matching pairs
        """
        try:
            url = f"{self.base_url}/latest/dex/search"
            params = {"q": query}
            
            self.logger.debug("Searching pairs", query=query)
            
            response = await self._make_request(url, params=params)
            pairs = response.get("pairs", [])
            
            formatted_pairs = []
            for pair in pairs[:limit]:
                if self._is_valid_pair(pair):
                    formatted_pairs.append(self._format_pair_data(pair))
            
            self.logger.info("Pair search completed", query=query, count=len(formatted_pairs))
            return formatted_pairs
            
        except Exception as e:
            self.logger.error("Failed to search pairs", error=str(e), query=query)
            raise
    
    def _is_valid_pair(self, pair: Dict[str, Any]) -> bool:
        """Check if pair data is valid and has minimum required fields."""
        required_fields = ["pairAddress", "baseToken", "quoteToken", "priceUsd"]
        
        for field in required_fields:
            if field not in pair or pair[field] is None:
                return False
        
        # Check if base token has required fields
        base_token = pair.get("baseToken", {})
        if not base_token.get("address") or not base_token.get("symbol"):
            return False
        
        # Check if quote token has required fields
        quote_token = pair.get("quoteToken", {})
        if not quote_token.get("address") or not quote_token.get("symbol"):
            return False
        
        return True
    
    def _format_pair_data(self, pair: Dict[str, Any]) -> Dict[str, Any]:
        """Format pair data into standardized structure."""
        base_token = pair.get("baseToken", {})
        quote_token = pair.get("quoteToken", {})
        volume = pair.get("volume", {})
        txns = pair.get("txns", {})
        price_change = pair.get("priceChange", {})
        liquidity = pair.get("liquidity", {})
        
        return {
            "pair_address": pair.get("pairAddress"),
            "chain_id": pair.get("chainId"),
            "dex_id": pair.get("dexId"),
            "url": pair.get("url"),
            "base_token": {
                "address": base_token.get("address"),
                "name": base_token.get("name"),
                "symbol": base_token.get("symbol")
            },
            "quote_token": {
                "address": quote_token.get("address"),
                "name": quote_token.get("name"),
                "symbol": quote_token.get("symbol")
            },
            "price_native": pair.get("priceNative"),
            "price_usd": pair.get("priceUsd"),
            "volume_24h": volume.get("h24", 0),
            "volume_6h": volume.get("h6", 0),
            "volume_1h": volume.get("h1", 0),
            "txns_24h": txns.get("h24", {}).get("buys", 0) + txns.get("h24", {}).get("sells", 0),
            "txns_6h": txns.get("h6", {}).get("buys", 0) + txns.get("h6", {}).get("sells", 0),
            "txns_1h": txns.get("h1", {}).get("buys", 0) + txns.get("h1", {}).get("sells", 0),
            "buys_24h": txns.get("h24", {}).get("buys", 0),
            "sells_24h": txns.get("h24", {}).get("sells", 0),
            "price_change_24h": price_change.get("h24", 0),
            "price_change_6h": price_change.get("h6", 0),
            "price_change_1h": price_change.get("h1", 0),
            "liquidity_usd": liquidity.get("usd", 0),
            "liquidity_base": liquidity.get("base", 0),
            "liquidity_quote": liquidity.get("quote", 0),
            "fdv": pair.get("fdv"),
            "market_cap": pair.get("marketCap"),
            "pair_created_at": pair.get("pairCreatedAt"),
            "labels": pair.get("labels", []),
            "info": pair.get("info", {}),
            "boosts": pair.get("boosts", {})
        }
    
    async def get_high_volume_pairs(self, min_volume: float = 100000) -> List[Dict[str, Any]]:
        """
        Get pairs with high trading volume.
        
        Args:
            min_volume: Minimum 24h volume in USD
            
        Returns:
            List of high volume pairs
        """
        try:
            # Search for major tokens to find high volume pairs
            major_tokens = ["ETH", "BTC", "USDC", "USDT", "WETH", "BNB"]
            high_volume_pairs = []
            
            for token in major_tokens:
                pairs = await self.search_pairs(token, limit=10)
                
                for pair in pairs:
                    volume_24h = pair.get("volume_24h", 0)
                    if volume_24h >= min_volume:
                        high_volume_pairs.append(pair)
                
                await asyncio.sleep(0.25)  # Rate limiting
            
            # Remove duplicates and sort by volume
            unique_pairs = {}
            for pair in high_volume_pairs:
                pair_addr = pair.get("pair_address")
                if pair_addr and pair_addr not in unique_pairs:
                    unique_pairs[pair_addr] = pair
            
            sorted_pairs = sorted(
                unique_pairs.values(),
                key=lambda x: x.get("volume_24h", 0),
                reverse=True
            )
            
            result = sorted_pairs[:20]  # Top 20
            self.logger.info("High volume pairs fetched", count=len(result), min_volume=min_volume)
            
            return result
            
        except Exception as e:
            self.logger.error("Failed to get high volume pairs", error=str(e))
            raise
