"""
CryptoQuant API scraper for on-chain data and exchange flows.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog

from alpha_grid.agents.scrapers.base_scraper import BaseScraper
from alpha_grid.core.config import config_manager


logger = structlog.get_logger(__name__)


class CryptoQuantScraper(BaseScraper):
    """CryptoQuant API scraper for on-chain metrics and exchange flows."""
    
    def __init__(self):
        """Initialize CryptoQuant scraper."""
        api_key = config_manager.get_decrypted_key('cryptoquant_api_key')
        super().__init__(
            name="CryptoQuant",
            rate_limit=config_manager.settings.cryptoquant_rate_limit,
            api_key=api_key,
            base_url="https://api.cryptoquant.com/v1"
        )
        
        # Cache for reducing API calls
        self._onchain_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 600  # 10 minutes for on-chain data
        
        self.logger = logger.bind(scraper="CryptoQuant")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for CryptoQuant API."""
        return {
            "Authorization": f"Bearer {self.api_key}"
        }
    
    async def health_check(self) -> bool:
        """Check if CryptoQuant API is healthy."""
        if not self.api_key:
            self.logger.warning("CryptoQuant API key not found. Health check skipped.")
            return True
        try:
            # Test with discovery endpoints
            url = f"{self.base_url}/discovery/endpoints"
            response = await self._make_request(url)
            return "result" in response and "status" in response
        except Exception as e:
            self.logger.error("Health check failed", error=str(e))
            return False
    
    async def get_whale_flows(self, asset: str = "btc", limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get whale transaction flows for specified asset.
        
        Args:
            asset: Asset symbol (btc, eth, etc.)
            limit: Number of transactions to return
            
        Returns:
            List of whale flow data
        """
        try:
            # Check cache first
            cache_key = f"whale_flows_{asset}_{limit}"
            if self._is_cache_valid() and cache_key in self._onchain_cache:
                self.logger.debug("Returning cached whale flows", asset=asset)
                return self._onchain_cache[cache_key]
            
            url = f"{self.base_url}/{asset}/flow/whale"
            params = {"limit": limit}
            
            self.logger.debug("Fetching whale flows", asset=asset)
            
            response = await self._make_request(url, params=params)
            
            if "result" not in response:
                raise ValueError(f"Invalid response format from whale flows API for {asset}")
            
            whale_flows = []
            for flow in response["result"]:
                formatted_flow = {
                    "timestamp": flow.get("timestamp"),
                    "readable_timestamp": self._format_timestamp(flow.get("timestamp")),
                    "hash": flow.get("hash"),
                    "from_address": flow.get("from_address"),
                    "to_address": flow.get("to_address"),
                    "value": float(flow.get("value", 0)),
                    "value_usd": float(flow.get("value_usd", 0)),
                    "exchange": flow.get("exchange"),
                    "flow_type": flow.get("flow_type"),  # inflow, outflow
                    "asset": asset.upper()
                }
                whale_flows.append(formatted_flow)
            
            # Cache the result
            self._onchain_cache[cache_key] = whale_flows
            self._cache_timestamp = datetime.now()
            
            self.logger.info("Whale flows fetched", asset=asset, count=len(whale_flows))
            return whale_flows
            
        except Exception as e:
            self.logger.error("Failed to get whale flows", asset=asset, error=str(e))
            # Return simulated data for demonstration
            return self._get_simulated_whale_flows(asset, limit)
    
    async def get_exchange_balances(self, asset: str = "btc") -> Dict[str, Any]:
        """
        Get exchange balance data for specified asset.
        
        Args:
            asset: Asset symbol (btc, eth, etc.)
            
        Returns:
            Exchange balance data
        """
        try:
            url = f"{self.base_url}/{asset}/exchange_balance"
            
            self.logger.debug("Fetching exchange balances", asset=asset)
            
            response = await self._make_request(url)
            
            if "result" not in response:
                raise ValueError(f"Invalid response format from exchange balance API for {asset}")
            
            balance_data = {
                "asset": asset.upper(),
                "total_balance": float(response["result"].get("total_balance", 0)),
                "total_balance_usd": float(response["result"].get("total_balance_usd", 0)),
                "exchanges": [],
                "balance_change_24h": float(response["result"].get("balance_change_24h", 0)),
                "balance_change_7d": float(response["result"].get("balance_change_7d", 0)),
                "last_updated": response["result"].get("timestamp"),
                "readable_last_updated": self._format_timestamp(response["result"].get("timestamp"))
            }
            
            # Process individual exchange data
            for exchange_data in response["result"].get("exchanges", []):
                exchange_info = {
                    "exchange": exchange_data.get("exchange"),
                    "balance": float(exchange_data.get("balance", 0)),
                    "balance_usd": float(exchange_data.get("balance_usd", 0)),
                    "percentage": float(exchange_data.get("percentage", 0)),
                    "change_24h": float(exchange_data.get("change_24h", 0))
                }
                balance_data["exchanges"].append(exchange_info)
            
            self.logger.info("Exchange balances fetched", asset=asset)
            return balance_data
            
        except Exception as e:
            self.logger.error("Failed to get exchange balances", asset=asset, error=str(e))
            # Return simulated data for demonstration
            return self._get_simulated_exchange_balances(asset)
    
    async def get_network_metrics(self, asset: str = "btc") -> Dict[str, Any]:
        """
        Get network metrics for specified asset.
        
        Args:
            asset: Asset symbol (btc, eth, etc.)
            
        Returns:
            Network metrics data
        """
        try:
            url = f"{self.base_url}/{asset}/network-data/addresses_count"
            
            self.logger.debug("Fetching network metrics", asset=asset)
            
            response = await self._make_request(url)
            
            if "result" not in response:
                raise ValueError(f"Invalid response format from network metrics API for {asset}")
            
            metrics = {
                "asset": asset.upper(),
                "active_addresses": response["result"].get("active_addresses", 0),
                "new_addresses": response["result"].get("new_addresses", 0),
                "total_addresses": response["result"].get("total_addresses", 0),
                "network_hash_rate": response["result"].get("hash_rate", 0),
                "difficulty": response["result"].get("difficulty", 0),
                "timestamp": response["result"].get("timestamp"),
                "readable_timestamp": self._format_timestamp(response["result"].get("timestamp"))
            }
            
            self.logger.info("Network metrics fetched", asset=asset)
            return metrics
            
        except Exception as e:
            self.logger.error("Failed to get network metrics", asset=asset, error=str(e))
            # Return simulated data for demonstration
            return self._get_simulated_network_metrics(asset)
    
    async def get_onchain_summary(self, assets: List[str] = None) -> Dict[str, Any]:
        """
        Get comprehensive on-chain summary.
        
        Args:
            assets: List of assets to analyze
            
        Returns:
            Complete on-chain analysis
        """
        try:
            if assets is None:
                assets = ["btc", "eth"]
            
            summary = {
                "assets_analyzed": len(assets),
                "whale_activity": {},
                "exchange_flows": {},
                "network_health": {},
                "market_insights": "",
                "last_updated": datetime.now().isoformat()
            }
            
            for asset in assets:
                try:
                    # Get whale flows
                    whale_flows = await self.get_whale_flows(asset, limit=50)
                    
                    # Get exchange balances
                    exchange_balances = await self.get_exchange_balances(asset)
                    
                    # Get network metrics
                    network_metrics = await self.get_network_metrics(asset)
                    
                    # Analyze whale activity
                    large_flows = [f for f in whale_flows if f.get("value_usd", 0) > 1_000_000]
                    inflows = [f for f in whale_flows if f.get("flow_type") == "inflow"]
                    outflows = [f for f in whale_flows if f.get("flow_type") == "outflow"]
                    
                    summary["whale_activity"][asset] = {
                        "total_flows": len(whale_flows),
                        "large_flows": len(large_flows),
                        "inflows": len(inflows),
                        "outflows": len(outflows),
                        "net_flow": len(inflows) - len(outflows),
                        "total_volume_usd": sum(f.get("value_usd", 0) for f in whale_flows)
                    }
                    
                    summary["exchange_flows"][asset] = {
                        "total_balance": exchange_balances.get("total_balance", 0),
                        "total_balance_usd": exchange_balances.get("total_balance_usd", 0),
                        "balance_change_24h": exchange_balances.get("balance_change_24h", 0),
                        "top_exchanges": exchange_balances.get("exchanges", [])[:5]
                    }
                    
                    summary["network_health"][asset] = {
                        "active_addresses": network_metrics.get("active_addresses", 0),
                        "new_addresses": network_metrics.get("new_addresses", 0),
                        "network_hash_rate": network_metrics.get("network_hash_rate", 0)
                    }
                    
                    # Rate limiting between assets
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    self.logger.warning("Failed to get data for asset", asset=asset, error=str(e))
                    continue
            
            # Generate market insights
            summary["market_insights"] = self._generate_onchain_insights(summary)
            
            self.logger.info("On-chain summary generated", assets=len(assets))
            return summary
            
        except Exception as e:
            self.logger.error("Failed to generate on-chain summary", error=str(e))
            raise
    
    def _get_simulated_whale_flows(self, asset: str, limit: int) -> List[Dict[str, Any]]:
        """Get simulated whale flows for demonstration."""
        flows = []
        for i in range(min(limit, 10)):
            flow = {
                "timestamp": int((datetime.now() - timedelta(hours=i)).timestamp()),
                "readable_timestamp": (datetime.now() - timedelta(hours=i)).strftime("%Y-%m-%d %H:%M:%S"),
                "hash": f"0x{''.join(['a', 'b', 'c', 'd', 'e', 'f'][j % 6] for j in range(64))}",
                "from_address": f"0x{''.join(['1', '2', '3', '4', '5', '6'][j % 6] for j in range(40))}",
                "to_address": f"0x{''.join(['7', '8', '9', 'a', 'b', 'c'][j % 6] for j in range(40))}",
                "value": 100 + i * 50,
                "value_usd": (100 + i * 50) * (50000 if asset == "btc" else 3000),
                "exchange": ["Binance", "Coinbase", "Kraken", "Bitfinex"][i % 4],
                "flow_type": ["inflow", "outflow"][i % 2],
                "asset": asset.upper()
            }
            flows.append(flow)
        return flows
    
    def _get_simulated_exchange_balances(self, asset: str) -> Dict[str, Any]:
        """Get simulated exchange balances for demonstration."""
        return {
            "asset": asset.upper(),
            "total_balance": 2_500_000 if asset == "btc" else 15_000_000,
            "total_balance_usd": 125_000_000_000 if asset == "btc" else 45_000_000_000,
            "exchanges": [
                {"exchange": "Binance", "balance": 800_000, "balance_usd": 40_000_000_000, "percentage": 32.0, "change_24h": -2.5},
                {"exchange": "Coinbase", "balance": 600_000, "balance_usd": 30_000_000_000, "percentage": 24.0, "change_24h": 1.2},
                {"exchange": "Kraken", "balance": 400_000, "balance_usd": 20_000_000_000, "percentage": 16.0, "change_24h": -0.8},
                {"exchange": "Bitfinex", "balance": 300_000, "balance_usd": 15_000_000_000, "percentage": 12.0, "change_24h": 0.5},
                {"exchange": "Others", "balance": 400_000, "balance_usd": 20_000_000_000, "percentage": 16.0, "change_24h": -1.1}
            ],
            "balance_change_24h": -1.5,
            "balance_change_7d": -3.2,
            "last_updated": int(datetime.now().timestamp()),
            "readable_last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def _get_simulated_network_metrics(self, asset: str) -> Dict[str, Any]:
        """Get simulated network metrics for demonstration."""
        return {
            "asset": asset.upper(),
            "active_addresses": 950_000 if asset == "btc" else 650_000,
            "new_addresses": 15_000 if asset == "btc" else 25_000,
            "total_addresses": 45_000_000 if asset == "btc" else 220_000_000,
            "network_hash_rate": 450_000_000_000_000_000 if asset == "btc" else 850_000_000_000_000,
            "difficulty": 62_000_000_000_000 if asset == "btc" else 16_500_000_000_000_000,
            "timestamp": int(datetime.now().timestamp()),
            "readable_timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def _generate_onchain_insights(self, summary: Dict[str, Any]) -> str:
        """Generate insights based on on-chain data."""
        insights = []
        
        # Analyze whale activity
        total_whale_volume = sum(
            data.get("total_volume_usd", 0) 
            for data in summary.get("whale_activity", {}).values()
        )
        
        if total_whale_volume > 1_000_000_000:  # $1B
            insights.append("High whale activity detected (>$1B volume)")
        
        # Analyze exchange flows
        for asset, flow_data in summary.get("exchange_flows", {}).items():
            balance_change = flow_data.get("balance_change_24h", 0)
            if balance_change < -5:
                insights.append(f"{asset.upper()} exchange outflows accelerating")
            elif balance_change > 5:
                insights.append(f"{asset.upper()} exchange inflows increasing")
        
        # Analyze network health
        for asset, network_data in summary.get("network_health", {}).items():
            active_addresses = network_data.get("active_addresses", 0)
            if active_addresses > 800_000:
                insights.append(f"{asset.upper()} network activity strong")
        
        return " | ".join(insights) if insights else "Standard on-chain activity levels"
    
    def _format_timestamp(self, timestamp) -> str:
        """Format timestamp to readable string."""
        try:
            if timestamp:
                if isinstance(timestamp, int):
                    dt = datetime.fromtimestamp(timestamp)
                else:
                    dt = datetime.fromisoformat(str(timestamp).replace('Z', '+00:00'))
                return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            pass
        return "Unknown"
    
    def _is_cache_valid(self) -> bool:
        """Check if cache is still valid."""
        if self._cache_timestamp is None:
            return False
        
        return (datetime.now() - self._cache_timestamp).total_seconds() < self._cache_ttl
