"""
Binance Public API scraper for derivatives and futures data.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog

from .base_scraper import BaseScraper
from alpha_grid.core.config import config_manager


logger = structlog.get_logger(__name__)


class BinanceScraper(BaseScraper):
    """Binance Public API scraper for derivatives and futures market data."""
    
    def __init__(self):
        """Initialize Binance scraper."""
        super().__init__(
            name="Binance",
            rate_limit=config_manager.settings.binance_rate_limit,
            api_key=None,  # No API key required for public endpoints
            base_url="https://fapi.binance.com"  # Futures API
        )
        
        # Cache for reducing API calls
        self._derivatives_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 60  # 1 minute for derivatives data
        
        self.logger = logger.bind(scraper="Binance")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers (none required for public API)."""
        return {}
    
    async def health_check(self) -> bool:
        """Check if Binance API is healthy."""
        try:
            url = f"{self.base_url}/fapi/v1/ping"
            response = await self._make_request(url)
            return response == {}  # Ping returns empty object on success
        except Exception as e:
            self.logger.error("Health check failed", error=str(e))
            return False
    
    async def get_funding_rates(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get current funding rates for futures contracts.
        
        Args:
            limit: Number of symbols to return
            
        Returns:
            List of funding rate data
        """
        try:
            # Check cache first
            cache_key = f"funding_rates_{limit}"
            if self._is_cache_valid() and cache_key in self._derivatives_cache:
                self.logger.debug("Returning cached funding rates")
                return self._derivatives_cache[cache_key]
            
            url = f"{self.base_url}/fapi/v1/premiumIndex"
            
            self.logger.debug("Fetching funding rates")
            
            response = await self._make_request(url)
            
            if not isinstance(response, list):
                raise ValueError("Invalid response format from Binance funding rates API")
            
            # Process and format funding rates
            funding_rates = []
            for item in response[:limit]:
                try:
                    funding_rate = {
                        "symbol": item.get("symbol"),
                        "mark_price": float(item.get("markPrice", 0)),
                        "index_price": float(item.get("indexPrice", 0)),
                        "estimated_settle_price": float(item.get("estimatedSettlePrice", 0)),
                        "last_funding_rate": float(item.get("lastFundingRate", 0)),
                        "interest_rate": float(item.get("interestRate", 0)),
                        "next_funding_time": int(item.get("nextFundingTime", 0)),
                        "time": int(item.get("time", 0)),
                        "funding_rate_percentage": round(float(item.get("lastFundingRate", 0)) * 100, 4),
                        "annualized_rate": round(float(item.get("lastFundingRate", 0)) * 365 * 3 * 100, 2)  # 3 times daily
                    }
                    
                    # Add interpretation
                    funding_rate["interpretation"] = self._interpret_funding_rate(
                        funding_rate["last_funding_rate"]
                    )
                    
                    funding_rates.append(funding_rate)
                    
                except (ValueError, TypeError) as e:
                    self.logger.warning("Failed to process funding rate item", error=str(e))
                    continue
            
            # Sort by absolute funding rate (most extreme first)
            funding_rates.sort(key=lambda x: abs(x["last_funding_rate"]), reverse=True)
            
            # Cache the result
            self._derivatives_cache[cache_key] = funding_rates
            self._cache_timestamp = datetime.now()
            
            self.logger.info("Funding rates fetched", count=len(funding_rates))
            return funding_rates
            
        except Exception as e:
            self.logger.error("Failed to get funding rates", error=str(e))
            raise
    
    async def get_open_interest(self, symbols: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Get open interest data for futures contracts.

        Args:
            symbols: List of symbols to query (if None, gets top symbols)

        Returns:
            List of open interest data
        """
        try:
            if symbols is None:
                # Get valid futures symbols only
                valid_symbols = await self._get_valid_futures_symbols(limit=20)
                symbols = valid_symbols
            else:
                # Filter provided symbols to only valid futures symbols
                valid_symbols = await self._get_valid_futures_symbols()
                symbols = [s for s in symbols if s in valid_symbols]

            open_interest_data = []
            failed_symbols = []

            for symbol in symbols:
                try:
                    url = f"{self.base_url}/fapi/v1/openInterest"
                    params = {"symbol": symbol}

                    response = await self._make_request(url, params=params)

                    if response:
                        oi_data = {
                            "symbol": symbol,
                            "open_interest": float(response.get("openInterest", 0)),
                            "time": int(response.get("time", 0)),
                            "readable_time": self._format_timestamp(response.get("time"))
                        }

                        # Get additional market data for context
                        market_data = await self._get_symbol_market_data(symbol)
                        if market_data:
                            oi_data.update({
                                "price": float(market_data.get("price", 0)),
                                "volume_24h": float(market_data.get("volume", 0)),
                                "price_change_24h": float(market_data.get("priceChangePercent", 0)),
                                "oi_value_usd": oi_data["open_interest"] * float(market_data.get("price", 0))
                            })

                        open_interest_data.append(oi_data)

                    # Rate limiting
                    await asyncio.sleep(0.1)

                except Exception as e:
                    # Don't retry on 400 errors (invalid symbols)
                    if "400" in str(e):
                        failed_symbols.append(symbol)
                        self.logger.debug("Symbol not available for futures", symbol=symbol)
                    else:
                        self.logger.warning("Failed to get OI for symbol", symbol=symbol, error=str(e))
                    continue

            # Sort by OI value
            open_interest_data.sort(key=lambda x: x.get("oi_value_usd", 0), reverse=True)

            self.logger.info("Open interest data fetched", count=len(open_interest_data),
                           failed_symbols=len(failed_symbols))
            return open_interest_data

        except Exception as e:
            self.logger.error("Failed to get open interest", error=str(e))
            raise

    async def _get_valid_futures_symbols(self, limit: Optional[int] = None) -> List[str]:
        """
        Get list of valid futures symbols from exchange info.

        Args:
            limit: Maximum number of symbols to return

        Returns:
            List of valid futures symbols
        """
        try:
            # Cache valid symbols to avoid repeated API calls
            if not hasattr(self, '_valid_futures_symbols'):
                url = f"{self.base_url}/fapi/v1/exchangeInfo"
                response = await self._make_request(url)

                if response and "symbols" in response:
                    # Filter for active USDT perpetual contracts
                    valid_symbols = []
                    for symbol_info in response["symbols"]:
                        if (symbol_info.get("status") == "TRADING" and
                            symbol_info.get("contractType") == "PERPETUAL" and
                            symbol_info.get("quoteAsset") == "USDT"):
                            valid_symbols.append(symbol_info["symbol"])

                    self._valid_futures_symbols = valid_symbols
                    self.logger.debug("Valid futures symbols cached", count=len(valid_symbols))
                else:
                    # Fallback to known good symbols
                    self._valid_futures_symbols = [
                        "BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT", "XRPUSDT",
                        "SOLUSDT", "DOGEUSDT", "DOTUSDT", "MATICUSDT", "LTCUSDT",
                        "AVAXUSDT", "LINKUSDT", "ATOMUSDT", "NEARUSDT", "APTUSDT"
                    ]
                    self.logger.warning("Using fallback futures symbols")

            symbols = self._valid_futures_symbols
            if limit:
                symbols = symbols[:limit]

            return symbols

        except Exception as e:
            self.logger.error("Failed to get valid futures symbols", error=str(e))
            # Return safe fallback symbols
            fallback_symbols = [
                "BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT", "XRPUSDT",
                "SOLUSDT", "DOGEUSDT", "DOTUSDT", "MATICUSDT", "LTCUSDT"
            ]
            return fallback_symbols[:limit] if limit else fallback_symbols

    async def get_liquidation_data(self) -> Dict[str, Any]:
        """
        Get liquidation data by analyzing funding rates and OI changes.
        
        Returns:
            Liquidation analysis data
        """
        try:
            # Get funding rates for analysis
            funding_rates = await self.get_funding_rates(limit=20)
            
            # Analyze for potential liquidation scenarios
            high_funding = [fr for fr in funding_rates if abs(fr["last_funding_rate"]) > 0.01]  # >1%
            
            liquidation_analysis = {
                "high_funding_symbols": len(high_funding),
                "extreme_funding_rates": high_funding[:5],  # Top 5 most extreme
                "average_funding_rate": sum(fr["last_funding_rate"] for fr in funding_rates) / len(funding_rates) if funding_rates else 0,
                "funding_rate_std": self._calculate_std([fr["last_funding_rate"] for fr in funding_rates]),
                "liquidation_risk": self._assess_liquidation_risk(funding_rates),
                "timestamp": datetime.now().isoformat()
            }
            
            self.logger.info("Liquidation analysis completed")
            return liquidation_analysis
            
        except Exception as e:
            self.logger.error("Failed to get liquidation data", error=str(e))
            raise
    
    async def get_derivatives_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive derivatives market summary.
        
        Returns:
            Complete derivatives market analysis
        """
        try:
            # Get all derivative data
            funding_rates = await self.get_funding_rates(limit=30)
            open_interest = await self.get_open_interest()
            liquidation_data = await self.get_liquidation_data()
            
            # Calculate market metrics
            total_oi_value = sum(oi.get("oi_value_usd", 0) for oi in open_interest)
            avg_funding_rate = sum(fr["last_funding_rate"] for fr in funding_rates) / len(funding_rates) if funding_rates else 0
            
            # Determine market sentiment
            sentiment = self._determine_derivatives_sentiment(funding_rates, liquidation_data)
            
            summary = {
                "funding_rates": {
                    "count": len(funding_rates),
                    "average_rate": round(avg_funding_rate * 100, 4),
                    "extreme_rates": len([fr for fr in funding_rates if abs(fr["last_funding_rate"]) > 0.005]),
                    "top_rates": funding_rates[:5]
                },
                "open_interest": {
                    "total_symbols": len(open_interest),
                    "total_value_usd": total_oi_value,
                    "top_oi": open_interest[:5]
                },
                "liquidation_analysis": liquidation_data,
                "market_sentiment": sentiment,
                "last_updated": datetime.now().isoformat()
            }
            
            self.logger.info("Derivatives summary generated")
            return summary
            
        except Exception as e:
            self.logger.error("Failed to generate derivatives summary", error=str(e))
            raise
    
    async def _get_top_symbols_by_volume(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get top symbols by 24h volume."""
        try:
            url = f"{self.base_url}/fapi/v1/ticker/24hr"
            
            response = await self._make_request(url)
            
            if not isinstance(response, list):
                return []
            
            # Sort by volume and return top symbols
            sorted_symbols = sorted(response, key=lambda x: float(x.get("volume", 0)), reverse=True)
            return sorted_symbols[:limit]
            
        except Exception as e:
            self.logger.warning("Failed to get top symbols", error=str(e))
            return []
    
    async def _get_symbol_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get market data for a specific symbol."""
        try:
            url = f"{self.base_url}/fapi/v1/ticker/24hr"
            params = {"symbol": symbol}
            
            response = await self._make_request(url, params=params)
            return response
            
        except Exception as e:
            self.logger.warning("Failed to get market data for symbol", symbol=symbol, error=str(e))
            return None
    
    def _format_timestamp(self, timestamp) -> str:
        """Format timestamp to readable string."""
        try:
            if timestamp:
                dt = datetime.fromtimestamp(int(timestamp) / 1000)  # Binance uses milliseconds
                return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            pass
        return "Unknown"
    
    def _interpret_funding_rate(self, rate: float) -> str:
        """Interpret funding rate value."""
        rate_percent = rate * 100
        
        if rate_percent > 0.1:
            return "Very High Positive - Longs paying shorts heavily"
        elif rate_percent > 0.05:
            return "High Positive - Longs paying shorts"
        elif rate_percent > 0.01:
            return "Moderate Positive - Slight long bias"
        elif rate_percent > -0.01:
            return "Neutral - Balanced funding"
        elif rate_percent > -0.05:
            return "Moderate Negative - Slight short bias"
        elif rate_percent > -0.1:
            return "High Negative - Shorts paying longs"
        else:
            return "Very High Negative - Shorts paying longs heavily"
    
    def _calculate_std(self, values: List[float]) -> float:
        """Calculate standard deviation."""
        if not values:
            return 0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance ** 0.5
    
    def _assess_liquidation_risk(self, funding_rates: List[Dict[str, Any]]) -> str:
        """Assess overall liquidation risk based on funding rates."""
        extreme_rates = [fr for fr in funding_rates if abs(fr["last_funding_rate"]) > 0.01]
        high_rates = [fr for fr in funding_rates if abs(fr["last_funding_rate"]) > 0.005]
        
        if len(extreme_rates) > 5:
            return "High - Multiple symbols with extreme funding rates"
        elif len(high_rates) > 10:
            return "Moderate - Several symbols with high funding rates"
        else:
            return "Low - Funding rates generally stable"
    
    def _determine_derivatives_sentiment(self, funding_rates: List[Dict[str, Any]], liquidation_data: Dict[str, Any]) -> str:
        """Determine overall derivatives market sentiment."""
        avg_funding = liquidation_data.get("average_funding_rate", 0)
        extreme_count = liquidation_data.get("high_funding_symbols", 0)
        
        if avg_funding > 0.005 and extreme_count > 5:
            return "Extremely Bullish - High long bias with liquidation risk"
        elif avg_funding > 0.002:
            return "Bullish - Positive funding rates indicate long bias"
        elif avg_funding < -0.005 and extreme_count > 5:
            return "Extremely Bearish - High short bias with liquidation risk"
        elif avg_funding < -0.002:
            return "Bearish - Negative funding rates indicate short bias"
        else:
            return "Neutral - Balanced derivatives market"
    
    def _is_cache_valid(self) -> bool:
        """Check if cache is still valid."""
        if self._cache_timestamp is None:
            return False
        
        return (datetime.now() - self._cache_timestamp).total_seconds() < self._cache_ttl
